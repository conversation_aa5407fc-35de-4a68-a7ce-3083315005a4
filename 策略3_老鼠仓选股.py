# -*- coding: utf-8 -*-
"""
策略3：老鼠仓选股自动交易
根据老鼠仓选股公式选出的股票到达涨停价时自动买入半仓
"""

import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
import os
from xtquant import xtdata
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount
from xtquant import xtconstant
import threading
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class Strategy3Callback(XtQuantTraderCallback):
    """策略3回调类"""

    def on_disconnected(self):
        logger.warning("策略3 - QMT连接断开")

    def on_stock_order(self, order):
        logger.info(f"策略3 - 委托回调: {order.stock_code}, 状态: {order.order_status}")

    def on_stock_trade(self, trade):
        logger.info(f"策略3 - 成交回调: {trade.stock_code}, 数量: {trade.traded_volume}")

    def on_order_error(self, order_error):
        logger.error(f"策略3 - 委托失败: {order_error.error_msg}")


class Strategy3_LaoshuCang_Trading:
    def __init__(self, account_id, mini_qmt_path=None):
        """
        初始化策略3：老鼠仓选股策略
        """
        self.account_id = account_id
        self.session_id = int(time.time()) + 2  # 避免与策略1、2冲突

        # 设置MiniQMT路径
        if mini_qmt_path is None:
            possible_paths = [
                r'D:\国金证券QMT交易端\userdata_mini',
                r'D:\国金证券QMT交易端\bin.x64\userdata_mini',
                r'D:\国金证券QMT交易端\bin\userdata_mini'
            ]

            mini_qmt_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    mini_qmt_path = path
                    logger.info(f"策略3 - 找到userdata_mini路径: {path}")
                    break

            if mini_qmt_path is None:
                raise Exception("策略3 - 未找到userdata_mini目录")

        try:
            # 初始化QMT连接
            self.trader = XtQuantTrader(mini_qmt_path, self.session_id)
            self.account = StockAccount(account_id)

            # 注册回调
            self.callback = Strategy3Callback()
            self.trader.register_callback(self.callback)

            # 启动并连接
            logger.info("策略3 - 启动QMT交易客户端...")
            self.trader.start()

            logger.info("策略3 - 建立QMT连接...")
            connect_result = self.trader.connect()
            if connect_result == 0:
                logger.info("策略3 - QMT连接成功")
            else:
                raise Exception(f"策略3 - QMT连接失败，错误码: {connect_result}")

            # 订阅账户
            self.trader.subscribe(self.account)

        except Exception as e:
            logger.error(f"策略3 - 初始化失败: {e}")
            raise

        # 策略状态
        self.is_running = False
        self.selected_stocks = []
        self.bought_stocks = set()

    def is_trading_time(self):
        """检查是否在交易时间内"""
        now = datetime.now()
        if now.weekday() >= 5:  # 跳过周末
            return False
        trading_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
        trading_end = now.replace(hour=14, minute=55, second=0, microsecond=0)
        return trading_start <= now <= trading_end

    def is_stock_selection_time(self):
        """检查是否在选股时间内（策略3在9:30之后选股）"""
        now = datetime.now()
        if now.weekday() >= 5:
            return False
        # 策略3在9:30之后选股
        selection_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
        selection_end = now.replace(hour=9, minute=35, second=0, microsecond=0)  # 给5分钟时间完成选股
        return selection_start <= now <= selection_end

    def get_stock_data(self, stock_code, period='1d', count=10):
        """获取股票数据"""
        try:
            data = xtdata.get_market_data(
                stock_list=[stock_code],
                period=period,
                count=count,
                fill_data=True
            )
            return data
        except Exception as e:
            logger.error(f"策略3 - 获取股票数据失败 {stock_code}: {e}")
            return None

    def is_main_board_stock(self, stock_code):
        """判断是否为主板股票"""
        return stock_code.startswith('60') or stock_code.startswith('00')

    def is_st_stock(self, stock_code):
        """判断是否为ST股票"""
        try:
            info = xtdata.get_instrument_detail(stock_code)
            name = info.get('name', '')
            return 'ST' in name or '*ST' in name
        except:
            return False

    def is_delisted_stock(self, stock_code):
        """判断是否为退市股票"""
        try:
            info = xtdata.get_instrument_detail(stock_code)
            name = info.get('name', '')
            return '退' in name or name.startswith('*')
        except:
            return False

    def is_limit_up(self, stock_code, current_price, prev_close):
        """判断是否涨停"""
        if self.is_main_board_stock(stock_code):
            limit_up_price = prev_close * 1.1  # 主板10%
        else:
            limit_up_price = prev_close * 1.2  # 创业板20%
        return abs(current_price - limit_up_price) < 0.01

    def calculate_price_change_rate(self, current_price, prev_close):
        """计算涨幅"""
        if prev_close == 0:
            return 0
        return (current_price - prev_close) / prev_close * 100

    def laoshucang_stock_selection(self):
        """老鼠仓选股逻辑
        AA1:=AMOUNT/VOL;
        BB1:=L< AA1*0.9;
        CC1:=(C-REF(C,1))/REF(C,1)*100> 1.2;
        DD1:=L< MA(C,5)*0.921;
        EE1:=VOL< MA(V,5)*1.5;
        老鼠仓:BB1 AND CC1 AND DD1 AND EE1;
        """
        try:
            all_stocks = xtdata.get_stock_list_in_sector('A股')
            selected_stocks = []

            logger.info(f"策略3 - 开始老鼠仓选股，共{len(all_stocks)}只股票")

            # 限制处理数量以提高效率
            for stock_code in all_stocks[:100]:
                try:
                    # 基本过滤条件：排除ST股和退市股
                    if self.is_st_stock(stock_code):
                        continue
                    if self.is_delisted_stock(stock_code):
                        continue

                    # 获取历史数据
                    data = self.get_stock_data(stock_code, period='1d', count=10)
                    if data is None or len(data) < 6:
                        continue

                    # 计算各项指标
                    closes = data['close'].values
                    lows = data['low'].values
                    volumes = data['volume'].values
                    amounts = data['amount'].values

                    # AA1:=AMOUNT/VOL (均价)
                    if volumes[-1] == 0:
                        continue
                    aa1 = amounts[-1] / volumes[-1]

                    # BB1:=L< AA1*0.9 (最低价小于均价的90%)
                    bb1 = lows[-1] < aa1 * 0.9

                    # CC1:=(C-REF(C,1))/REF(C,1)*100> 1.2 (涨幅大于1.2%)
                    cc1 = (closes[-1] - closes[-2]) / closes[-2] * 100 > 1.2

                    # DD1:=L< MA(C,5)*0.921 (最低价小于5日均价的92.1%)
                    if len(closes) >= 5:
                        ma5 = np.mean(closes[-5:])
                        dd1 = lows[-1] < ma5 * 0.921
                    else:
                        continue

                    # EE1:=VOL< MA(V,5)*1.5 (成交量小于5日均量的1.5倍)
                    if len(volumes) >= 5:
                        ma5_vol = np.mean(volumes[-5:])
                        ee1 = volumes[-1] < ma5_vol * 1.5
                    else:
                        continue

                    # 老鼠仓:BB1 AND CC1 AND DD1 AND EE1
                    if bb1 and cc1 and dd1 and ee1:
                        selected_stocks.append(stock_code)
                        logger.info(f"策略3 - 选中老鼠仓股票: {stock_code}")
                        logger.debug(f"  AA1={aa1:.2f}, BB1={bb1}, CC1={cc1}, DD1={dd1}, EE1={ee1}")

                except Exception as e:
                    logger.error(f"策略3 - 处理股票{stock_code}时出错: {e}")
                    continue

            return selected_stocks

        except Exception as e:
            logger.error(f"策略3 - 老鼠仓选股失败: {e}")
            return []

    def check_buy_condition(self, stock_code):
        """检查买入条件：达到涨停价"""
        try:
            current_data = xtdata.get_market_data([stock_code], period='tick', count=1)
            if current_data is None:
                return False

            current_price = current_data['lastPrice'][0]
            prev_close = current_data['preClose'][0]

            # 策略3：达到涨停价时买入
            return self.is_limit_up(stock_code, current_price, prev_close)

        except Exception as e:
            logger.error(f"策略3 - 检查买入条件失败 {stock_code}: {e}")
            return False

    def place_buy_order(self, stock_code):
        """下买单（半仓）"""
        try:
            # 获取账户资金
            account_info = self.trader.query_stock_asset(self.account)
            if account_info is None:
                logger.error("策略3 - 无法获取账户信息")
                return False

            available_cash = account_info.cash
            half_cash = available_cash * 0.5  # 半仓

            # 获取当前价格
            current_data = xtdata.get_market_data([stock_code], period='tick', count=1)
            if current_data is None:
                logger.error(f"策略3 - 无法获取{stock_code}当前价格")
                return False

            current_price = current_data['lastPrice'][0]
            buy_quantity = int(half_cash / current_price / 100) * 100

            if buy_quantity < 100:
                logger.warning(f"策略3 - 资金不足，无法买入{stock_code}")
                return False

            # 下买单
            order_id = self.trader.order_stock(
                account=self.account,
                stock_code=stock_code,
                order_type=xtconstant.STOCK_BUY,
                order_volume=buy_quantity,
                price_type=xtconstant.FIX_PRICE,
                price=current_price,
                strategy_name='strategy3_laoshucang',
                order_remark=f'LaoshuCang_{stock_code}'
            )

            if order_id > 0:
                logger.info(f"策略3 - 成功下买单: {stock_code}, 数量: {buy_quantity}, 价格: {current_price}")
                self.bought_stocks.add(stock_code)
                return True
            else:
                logger.error(f"策略3 - 下买单失败: {stock_code}")
                return False

        except Exception as e:
            logger.error(f"策略3 - 下买单异常 {stock_code}: {e}")
            return False

    def start_trading(self):
        """启动交易策略"""
        self.is_running = True
        logger.info("策略3 - 老鼠仓交易策略已启动")

        # 启动主交易线程
        trading_thread = threading.Thread(target=self.main_trading_loop)
        trading_thread.daemon = True
        trading_thread.start()

    def main_trading_loop(self):
        """主交易循环"""
        last_selection_date = None

        while self.is_running:
            try:
                current_time = datetime.now()
                today = current_time.date()

                # 选股时间：9:30-9:35（开盘后选股，每天只选一次）
                if self.is_stock_selection_time() and last_selection_date != today:
                    logger.info("策略3 - 开始老鼠仓选股（开盘后选股）...")
                    self.selected_stocks = self.laoshucang_stock_selection()
                    last_selection_date = today
                    logger.info(f"策略3 - 选股完成，共选中{len(self.selected_stocks)}只老鼠仓股票")

                # 交易时间：9:30-14:55（选股完成后开始监控买入）
                elif self.is_trading_time() and self.selected_stocks:  # 确保已完成选股
                    for stock_code in self.selected_stocks:
                        if stock_code not in self.bought_stocks:
                            if self.check_buy_condition(stock_code):
                                self.place_buy_order(stock_code)

                time.sleep(5)  # 5秒检查一次

            except Exception as e:
                logger.error(f"策略3 - 主交易循环异常: {e}")
                time.sleep(10)

    def run_strategy(self):
        """运行策略"""
        logger.info("策略3 - 启动老鼠仓自动交易策略...")
        self.start_trading()

        try:
            while self.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("策略3 - 接收到退出信号")
            self.is_running = False
            self.trader.unsubscribe(self.account)
            self.trader.stop()


if __name__ == "__main__":
    # 配置参数
    ACCOUNT_ID = "**********"  # 您的资金账号

    try:
        # 创建并运行策略3
        strategy3 = Strategy3_LaoshuCang_Trading(ACCOUNT_ID)
        strategy3.run_strategy()

    except Exception as e:
        logger.error(f"策略3 - 程序运行异常: {e}")
        print(f"策略3错误: {e}")
        print("请检查QMT客户端是否已启动并登录")