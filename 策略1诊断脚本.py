# -*- coding: utf-8 -*-
"""
策略1诊断脚本 - 帮助找出P1P2选股无法选出股票的具体原因
"""

import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from xtquant import xtdata
    from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
    from xtquant.xttype import StockAccount
    from xtquant import xtconstant
    QMT_AVAILABLE = True
    print("✓ QMT模块导入成功")
except ImportError as e:
    QMT_AVAILABLE = False
    print(f"✗ QMT模块导入失败: {e}")
    print("请在QMT环境中运行此脚本")


class P1P2Diagnostics:
    """P1P2选股诊断类"""
    
    def __init__(self):
        self.processed_count = 0
        self.data_error_count = 0
        self.filter_excluded_count = 0
        self.condition1_count = 0
        self.condition2_count = 0
        self.selected_count = 0
    
    def is_main_board_stock(self, stock_code):
        """判断是否为主板股票"""
        return stock_code.startswith('60') or stock_code.startswith('00')
    
    def get_stock_data(self, stock_code, period='1d', count=10):
        """获取股票数据"""
        if not QMT_AVAILABLE:
            return None
        
        try:
            data = xtdata.get_market_data(
                stock_list=[stock_code],
                period=period,
                count=count,
                fill_data=True
            )
            return data
        except Exception as e:
            logger.error(f"获取股票数据失败 {stock_code}: {e}")
            return None
    
    def calculate_p1p2(self, closes, opens):
        """计算P1P2指标"""
        if len(closes) < 4 or len(opens) < 4:
            return None, None, None, None
        
        # 使用昨日数据作为当前计算基准
        current_close = closes[-2]    # 昨日收盘价（作为当前）
        current_open = opens[-2]      # 昨日开盘价（作为当前）
        
        # REF(O,1) 是前一日开盘价
        ref_open_1 = opens[-3] if len(opens) >= 3 else opens[-2]
        
        # REF(O,2) 是前两日开盘价  
        ref_open_2 = opens[-4] if len(opens) >= 4 else opens[-3]
        
        # 前一日数据（用于计算REF(P1,1)和REF(P2,1)）
        prev_close = closes[-3] if len(closes) >= 3 else closes[-2]
        prev_open = opens[-3] if len(opens) >= 3 else opens[-2]
        prev_ref_open_1 = opens[-4] if len(opens) >= 4 else opens[-3]
        prev_ref_open_2 = opens[-5] if len(opens) >= 5 else opens[-4]

        # 计算P1指标：P1:=IF(C<REF(O,1) AND C<O,1,0);
        p1_current = 1 if (current_close < ref_open_1 and current_close < current_open) else 0
        p1_prev = 1 if (prev_close < prev_ref_open_1 and prev_close < prev_open) else 0

        # 计算P2指标：P2:=IF(C<REF(O,2) AND C<O,1,0);
        p2_current = 1 if (current_close < ref_open_2 and current_close < current_open) else 0
        p2_prev = 1 if (prev_close < prev_ref_open_2 and prev_close < prev_open) else 0

        return p1_current, p1_prev, p2_current, p2_prev
    
    def diagnose_single_stock(self, stock_code):
        """诊断单只股票"""
        print(f"\n{'='*50}")
        print(f"诊断股票: {stock_code}")
        print(f"{'='*50}")
        
        # 1. 检查股票代码格式
        if not self.is_main_board_stock(stock_code):
            print(f"✗ 非主板股票，已过滤")
            return False
        
        print(f"✓ 主板股票检查通过")
        
        # 2. 尝试获取数据
        print(f"正在获取历史数据...")
        data = self.get_stock_data(stock_code, period='1d', count=10)
        
        if data is None:
            print(f"✗ 无法获取股票数据")
            return False
        
        print(f"✓ 成功获取数据，数据长度: {len(data)}")
        
        if len(data) < 4:
            print(f"✗ 数据不足，只有{len(data)}天数据，需要至少4天")
            return False
        
        # 3. 检查数据内容
        closes = data['close'].values
        opens = data['open'].values
        
        print(f"收盘价: {closes[-5:]}")  # 显示最近5天
        print(f"开盘价: {opens[-5:]}")
        
        # 4. 计算P1P2指标
        p1_current, p1_prev, p2_current, p2_prev = self.calculate_p1p2(closes, opens)
        
        if p1_current is None:
            print(f"✗ P1P2计算失败")
            return False
        
        print(f"P1当前: {p1_current}, P1前值: {p1_prev}")
        print(f"P2当前: {p2_current}, P2前值: {p2_prev}")
        
        # 5. 检查选股条件
        condition1 = p1_current == 0 and p1_prev == 1
        condition2 = p2_current == 0 and p2_prev == 1
        
        print(f"条件1 (P1=0 AND REF(P1,1)=1): {condition1}")
        print(f"条件2 (P2=0 AND REF(P2,1)=1): {condition2}")
        
        if condition1 and condition2:
            print(f"✓ 符合P1P2选股条件！")
            return True
        else:
            print(f"✗ 不符合P1P2选股条件")
            if condition1:
                print(f"  - 满足条件1但不满足条件2")
            elif condition2:
                print(f"  - 满足条件2但不满足条件1")
            else:
                print(f"  - 两个条件都不满足")
            return False
    
    def diagnose_stock_list(self, max_stocks=50):
        """诊断股票列表"""
        if not QMT_AVAILABLE:
            print("QMT不可用，无法获取股票列表")
            return
        
        print(f"\n{'='*60}")
        print(f"批量诊断股票列表（最多{max_stocks}只）")
        print(f"{'='*60}")
        
        try:
            all_stocks = xtdata.get_stock_list_in_sector('A股')
            print(f"获取到{len(all_stocks)}只A股")
            
            # 只处理主板股票
            main_board_stocks = [s for s in all_stocks if self.is_main_board_stock(s)]
            print(f"其中主板股票{len(main_board_stocks)}只")
            
            # 限制处理数量
            test_stocks = main_board_stocks[:max_stocks]
            print(f"将测试前{len(test_stocks)}只股票")
            
            for i, stock_code in enumerate(test_stocks):
                print(f"\n[{i+1}/{len(test_stocks)}] 测试 {stock_code}")
                
                try:
                    self.processed_count += 1
                    
                    # 获取数据
                    data = self.get_stock_data(stock_code, period='1d', count=10)
                    if data is None or len(data) < 4:
                        self.data_error_count += 1
                        print(f"  ✗ 数据获取失败或不足")
                        continue
                    
                    # 计算P1P2
                    closes = data['close'].values
                    opens = data['open'].values
                    
                    p1_current, p1_prev, p2_current, p2_prev = self.calculate_p1p2(closes, opens)
                    
                    if p1_current is None:
                        self.data_error_count += 1
                        continue
                    
                    # 检查条件
                    condition1 = p1_current == 0 and p1_prev == 1
                    condition2 = p2_current == 0 and p2_prev == 1
                    
                    if condition1:
                        self.condition1_count += 1
                    if condition2:
                        self.condition2_count += 1
                    
                    if condition1 and condition2:
                        self.selected_count += 1
                        print(f"  ✓ 选中！P1({p1_current},{p1_prev}) P2({p2_current},{p2_prev})")
                    else:
                        print(f"  - 未选中 P1({p1_current},{p1_prev}) P2({p2_current},{p2_prev})")
                
                except Exception as e:
                    print(f"  ✗ 处理异常: {e}")
                    self.data_error_count += 1
                    continue
        
        except Exception as e:
            print(f"获取股票列表失败: {e}")
    
    def print_summary(self):
        """打印诊断总结"""
        print(f"\n{'='*60}")
        print(f"诊断总结")
        print(f"{'='*60}")
        print(f"处理股票数: {self.processed_count}")
        print(f"数据错误数: {self.data_error_count}")
        print(f"满足条件1的股票数: {self.condition1_count}")
        print(f"满足条件2的股票数: {self.condition2_count}")
        print(f"同时满足两个条件的股票数: {self.selected_count}")
        
        if self.processed_count > 0:
            print(f"数据获取成功率: {(self.processed_count - self.data_error_count) / self.processed_count * 100:.1f}%")
            print(f"条件1满足率: {self.condition1_count / self.processed_count * 100:.1f}%")
            print(f"条件2满足率: {self.condition2_count / self.processed_count * 100:.1f}%")
            print(f"最终选股成功率: {self.selected_count / self.processed_count * 100:.1f}%")
        
        print(f"\n可能的问题:")
        if self.data_error_count > self.processed_count * 0.5:
            print(f"- 数据获取问题严重，可能是QMT连接或权限问题")
        if self.selected_count == 0:
            print(f"- 没有选中任何股票，可能是:")
            print(f"  1. P1P2条件过于严格")
            print(f"  2. 当前市场状态不符合反转信号")
            print(f"  3. 数据时间窗口问题")
        if self.condition1_count == 0 and self.condition2_count == 0:
            print(f"- 所有股票都不满足任何条件，可能是算法问题")


def main():
    """主函数"""
    print("策略1 P1P2选股诊断脚本")
    print("当前时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    if not QMT_AVAILABLE:
        print("\n请在QMT环境中运行此脚本以获得完整诊断")
        return
    
    diagnostics = P1P2Diagnostics()
    
    # 选择诊断模式
    print("\n请选择诊断模式:")
    print("1. 诊断特定股票")
    print("2. 批量诊断股票列表")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == '1':
            stock_code = input("请输入股票代码 (如 600000.SH): ").strip()
            diagnostics.diagnose_single_stock(stock_code)
        
        elif choice == '2':
            max_stocks = input("请输入最大测试股票数 (默认50): ").strip()
            max_stocks = int(max_stocks) if max_stocks.isdigit() else 50
            diagnostics.diagnose_stock_list(max_stocks)
            diagnostics.print_summary()
        
        else:
            print("无效选择")
    
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"运行异常: {e}")


if __name__ == "__main__":
    main()
