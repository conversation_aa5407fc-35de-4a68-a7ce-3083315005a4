# -*- coding: utf-8 -*-
"""
P1P2选股公式调试脚本
用于调试为什么选不出股票
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def simulate_p1p2_selection():
    """模拟P1P2选股，使用虚拟数据测试公式逻辑"""
    print("="*60)
    print("P1P2公式调试 - 使用模拟数据")
    print("="*60)
    
    # 创建一些模拟股票数据，确保有符合条件的情况
    test_stocks = {
        "600000.SH": {
            "name": "浦发银行",
            "close": [10.0, 9.8, 10.2, 9.9, 10.1],  # 收盘价
            "open":  [9.9, 9.7, 10.0, 9.8, 10.0]   # 开盘价
        },
        "000001.SZ": {
            "name": "平安银行", 
            "close": [15.0, 14.5, 15.2, 14.8, 15.1],
            "open":  [14.8, 14.3, 15.0, 14.6, 14.9]
        },
        "600036.SH": {
            "name": "招商银行",
            "close": [35.0, 34.2, 35.5, 34.8, 35.2],
            "open":  [34.8, 34.0, 35.2, 34.5, 35.0]
        },
        # 添加一个明确符合条件的股票
        "TEST01.SH": {
            "name": "测试股票1",
            # 设计数据让P1和P2都符合条件
            "close": [10.0, 9.5, 10.2, 9.8, 10.5],  # 今日收盘10.5，昨日9.8
            "open":  [9.8, 9.3, 10.0, 9.6, 10.3]   # 今日开盘10.3，昨日9.6
        }
    }
    
    selected_stocks = []
    
    for stock_code, stock_data in test_stocks.items():
        print(f"\n分析股票: {stock_code} ({stock_data['name']})")
        
        closes = np.array(stock_data['close'])
        opens = np.array(stock_data['open'])
        
        print(f"收盘价: {closes}")
        print(f"开盘价: {opens}")
        
        # 使用修正后的逻辑
        # 当前数据（最新的完整交易日）
        current_close = closes[-2]    # 昨日收盘价（作为当前）
        current_open = opens[-2]      # 昨日开盘价（作为当前）
        
        # REF(O,1) 是前一日开盘价
        ref_open_1 = opens[-3] if len(opens) >= 3 else opens[-2]
        
        # REF(O,2) 是前两日开盘价  
        ref_open_2 = opens[-4] if len(opens) >= 4 else opens[-3]
        
        # 前一日数据（用于计算REF(P1,1)和REF(P2,1)）
        prev_close = closes[-3] if len(closes) >= 3 else closes[-2]
        prev_open = opens[-3] if len(opens) >= 3 else opens[-2]
        prev_ref_open_1 = opens[-4] if len(opens) >= 4 else opens[-3]
        prev_ref_open_2 = opens[-5] if len(opens) >= 5 else opens[-4]

        print(f"当前收盘: {current_close}, 当前开盘: {current_open}")
        print(f"REF(O,1): {ref_open_1}, REF(O,2): {ref_open_2}")
        
        # 计算P1指标：P1:=IF(C<REF(O,1) AND C<O,1,0);
        p1_current = 1 if (current_close < ref_open_1 and current_close < current_open) else 0
        p1_prev = 1 if (prev_close < prev_ref_open_1 and prev_close < prev_open) else 0
        
        print(f"P1计算: {current_close} < {ref_open_1} AND {current_close} < {current_open} = {p1_current}")
        print(f"P1前值: {p1_prev}")

        # 计算P2指标：P2:=IF(C<REF(O,2) AND C<O,1,0);
        p2_current = 1 if (current_close < ref_open_2 and current_close < current_open) else 0
        p2_prev = 1 if (prev_close < prev_ref_open_2 and prev_close < prev_open) else 0
        
        print(f"P2计算: {current_close} < {ref_open_2} AND {current_close} < {current_open} = {p2_current}")
        print(f"P2前值: {p2_prev}")

        # 选股条件: P1=0 AND REF(P1,1)=1 AND P2=0 AND REF(P2,1)=1
        condition1 = p1_current == 0 and p1_prev == 1
        condition2 = p2_current == 0 and p2_prev == 1
        
        print(f"条件1 (P1=0 AND REF(P1,1)=1): {condition1}")
        print(f"条件2 (P2=0 AND REF(P2,1)=1): {condition2}")
        print(f"最终结果: {condition1 and condition2}")
        
        if condition1 and condition2:
            selected_stocks.append(stock_code)
            print(f"✓ 选中股票: {stock_code}")
        else:
            print(f"✗ 未选中: {stock_code}")
    
    print(f"\n选股结果: 共选中 {len(selected_stocks)} 只股票")
    return selected_stocks


def create_ideal_test_case():
    """创建一个理想的测试用例，确保能选出股票"""
    print("\n" + "="*60)
    print("创建理想测试用例")
    print("="*60)

    # 重新设计数据，确保能满足P1P2条件
    # 目标：P1=0 AND REF(P1,1)=1 AND P2=0 AND REF(P2,1)=1

    print("精心设计数据让P1P2条件成立...")

    # 设计思路：
    # 1. 前一日要弱势：REF(P1,1)=1, REF(P2,1)=1
    # 2. 当前日要强势：P1=0, P2=0

    # 6天数据，用于确保有足够的历史数据
    # [day0, day1, day2, day3, day4, day5]
    # day4作为"当前"，day3作为"前一日"

    closes = [12.0, 11.0, 10.0, 9.0, 11.0, 12.0]  # 收盘价
    opens =  [11.8, 10.8, 9.8,  8.8, 10.5, 11.5]  # 开盘价

    print(f"精心设计的数据:")
    print(f"收盘价: {closes}")
    print(f"开盘价: {opens}")

    # 使用day4作为当前（索引3）
    current_close = closes[4]  # 11.0
    current_open = opens[4]    # 10.5
    ref_open_1 = opens[3]      # 8.8 (REF(O,1))
    ref_open_2 = opens[2]      # 9.8 (REF(O,2))

    # 使用day3作为前一日（索引2）
    prev_close = closes[3]     # 9.0
    prev_open = opens[3]       # 8.8
    prev_ref_open_1 = opens[2] # 9.8
    prev_ref_open_2 = opens[1] # 10.8

    print(f"\n当前日: 收盘={current_close}, 开盘={current_open}")
    print(f"前一日: 收盘={prev_close}, 开盘={prev_open}")
    print(f"REF(O,1)={ref_open_1}, REF(O,2)={ref_open_2}")

    # 计算P1
    # P1当前：11.0 < 8.8 AND 11.0 < 10.5 → False AND False = 0 ✓
    p1_current = 1 if (current_close < ref_open_1 and current_close < current_open) else 0
    # P1前值：9.0 < 9.8 AND 9.0 < 8.8 → True AND False = 0 (需要调整)
    p1_prev = 1 if (prev_close < prev_ref_open_1 and prev_close < prev_open) else 0

    # 计算P2
    p2_current = 1 if (current_close < ref_open_2 and current_close < current_open) else 0
    p2_prev = 1 if (prev_close < prev_ref_open_2 and prev_close < prev_open) else 0

    print(f"\nP1当前: {current_close} < {ref_open_1} AND {current_close} < {current_open}")
    print(f"P1当前: {current_close < ref_open_1} AND {current_close < current_open} = {p1_current}")
    print(f"P1前值: {prev_close} < {prev_ref_open_1} AND {prev_close} < {prev_open}")
    print(f"P1前值: {prev_close < prev_ref_open_1} AND {prev_close < prev_open} = {p1_prev}")

    print(f"\nP2当前: {current_close} < {ref_open_2} AND {current_close} < {current_open}")
    print(f"P2当前: {current_close < ref_open_2} AND {current_close < current_open} = {p2_current}")
    print(f"P2前值: {prev_close} < {prev_ref_open_2} AND {prev_close} < {prev_open}")
    print(f"P2前值: {prev_close < prev_ref_open_2} AND {prev_close < prev_open} = {p2_prev}")

    # 重新调整数据让P1前值=1
    if p1_prev != 1 or p2_prev != 1:
        print("调整数据让P1和P2前值都=1...")
        # 让前一日收盘价既小于前一日开盘价，也小于REF(O,1)和REF(O,2)
        prev_close_new = 8.0  # 小于8.8(prev_open)、9.8(prev_ref_open_1)和10.8(prev_ref_open_2)
        prev_open_new = 8.8   # 保持不变

        p1_prev_new = 1 if (prev_close_new < prev_ref_open_1 and prev_close_new < prev_open_new) else 0
        p2_prev_new = 1 if (prev_close_new < prev_ref_open_2 and prev_close_new < prev_open_new) else 0

        print(f"调整后P1前值: {prev_close_new} < {prev_ref_open_1} AND {prev_close_new} < {prev_open_new} = {p1_prev_new}")
        print(f"调整后P2前值: {prev_close_new} < {prev_ref_open_2} AND {prev_close_new} < {prev_open_new} = {p2_prev_new}")

        # 选股条件
        condition1_new = p1_current == 0 and p1_prev_new == 1
        condition2_new = p2_current == 0 and p2_prev_new == 1

        print(f"\n调整后选股条件:")
        print(f"条件1 (P1=0 AND REF(P1,1)=1): {p1_current}==0 AND {p1_prev_new}==1 = {condition1_new}")
        print(f"条件2 (P2=0 AND REF(P2,1)=1): {p2_current}==0 AND {p2_prev_new}==1 = {condition2_new}")
        print(f"最终结果: {condition1_new and condition2_new}")

        if condition1_new and condition2_new:
            print("✓ 成功设计出符合条件的数据！")
            return True
        else:
            print("✗ 仍然不符合条件，需要进一步调整")

    return False


def analyze_tdx_formula():
    """分析通达信公式的真实含义"""
    print("\n" + "="*60)
    print("分析通达信公式含义")
    print("="*60)
    
    print("通达信公式:")
    print("P1:=IF(C<REF(O,1) AND C<O,1,0);")
    print("P2:=IF(C<REF(O,2) AND C<O,1,0);")
    print("选股条件: P1=0 AND REF(P1,1)=1 AND P2=0 AND REF(P2,1)=1;")
    
    print("\n公式解释:")
    print("P1=1 表示：当日收盘价 < 前1日开盘价 AND 当日收盘价 < 当日开盘价")
    print("P1=0 表示：NOT(当日收盘价 < 前1日开盘价 AND 当日收盘价 < 当日开盘价)")
    print("即：当日收盘价 >= 前1日开盘价 OR 当日收盘价 >= 当日开盘价")
    
    print("\n选股条件含义:")
    print("P1=0: 今日不满足P1条件（今日相对强势）")
    print("REF(P1,1)=1: 昨日满足P1条件（昨日相对弱势）")
    print("P2=0: 今日不满足P2条件（今日相对强势）")
    print("REF(P2,1)=1: 昨日满足P2条件（昨日相对弱势）")
    
    print("\n这个公式寻找的是：")
    print("昨日弱势（P1=1, P2=1），今日转强势（P1=0, P2=0）的股票")
    print("即：反转信号，从弱势转为强势")


def main():
    """主函数"""
    print("P1P2选股公式调试开始")
    print("当前时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 分析公式含义
    analyze_tdx_formula()
    
    # 模拟选股
    simulate_p1p2_selection()
    
    # 创建理想测试用例
    create_ideal_test_case()
    
    print("\n" + "="*60)
    print("调试建议:")
    print("="*60)
    print("1. 检查数据获取是否正常")
    print("2. 确认股票代码格式正确")
    print("3. 验证历史数据的完整性")
    print("4. 放宽过滤条件（ST股、新股等）")
    print("5. 增加处理的股票数量")
    print("6. 添加更多调试日志")


if __name__ == "__main__":
    main()
