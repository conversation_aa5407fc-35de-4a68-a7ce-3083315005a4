# -*- coding: UTF-8 -*-
"""
@Project ：stock_code 
@File    ：策略2_股票池选股.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/9 15:41:45
"""
# -*- coding: utf-8 -*-
"""
策略2：股票池选股自动交易
从通达信股票池筛选涨幅大于7%且不涨停的股票，涨幅达到9.96%时自动买入半仓
修正：在9:30开盘后进行选股，而非9:25-9:30
"""

import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
import os
from xtquant import xtdata
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount
from xtquant import xtconstant
import threading
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class Strategy2Callback(XtQuantTraderCallback):
    """策略2回调类"""

    def on_disconnected(self):
        logger.warning("策略2 - QMT连接断开")

    def on_stock_order(self, order):
        logger.info(f"策略2 - 委托回调: {order.stock_code}, 状态: {order.order_status}")

    def on_stock_trade(self, trade):
        logger.info(f"策略2 - 成交回调: {trade.stock_code}, 数量: {trade.traded_volume}")

    def on_order_error(self, order_error):
        logger.error(f"策略2 - 委托失败: {order_error.error_msg}")


class Strategy2_StockPool_Trading:
    def __init__(self, account_id, stock_pool_path=None, mini_qmt_path=None):
        """
        初始化策略2：股票池选股策略
        """
        self.account_id = account_id
        self.session_id = int(time.time()) + 1  # 避免与策略1冲突

        # 股票池路径
        if stock_pool_path is None:
            self.stock_pool_path = r"D:\tdx\T0002\blocknew\1J2.blk"
        else:
            self.stock_pool_path = stock_pool_path

        # 设置MiniQMT路径
        if mini_qmt_path is None:
            possible_paths = [
                r'D:\国金证券QMT交易端\userdata_mini',
                r'D:\国金证券QMT交易端\bin.x64\userdata_mini',
                r'D:\国金证券QMT交易端\bin\userdata_mini'
            ]

            mini_qmt_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    mini_qmt_path = path
                    logger.info(f"策略2 - 找到userdata_mini路径: {path}")
                    break

            if mini_qmt_path is None:
                raise Exception("策略2 - 未找到userdata_mini目录")

        try:
            # 初始化QMT连接
            self.trader = XtQuantTrader(mini_qmt_path, self.session_id)
            self.account = StockAccount(account_id)

            # 注册回调
            self.callback = Strategy2Callback()
            self.trader.register_callback(self.callback)

            # 启动并连接
            logger.info("策略2 - 启动QMT交易客户端...")
            self.trader.start()

            logger.info("策略2 - 建立QMT连接...")
            connect_result = self.trader.connect()
            if connect_result == 0:
                logger.info("策略2 - QMT连接成功")
            else:
                raise Exception(f"策略2 - QMT连接失败，错误码: {connect_result}")

            # 订阅账户
            self.trader.subscribe(self.account)

        except Exception as e:
            logger.error(f"策略2 - 初始化失败: {e}")
            raise

        # 策略状态
        self.is_running = False
        self.selected_stocks = []
        self.bought_stocks = set()
        self.selection_completed = False  # 新增：标记是否已完成选股

    def is_trading_time(self):
        """检查是否在交易时间内"""
        now = datetime.now()
        if now.weekday() >= 5:  # 跳过周末
            return False
        trading_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
        trading_end = now.replace(hour=14, minute=55, second=0, microsecond=0)
        return trading_start <= now <= trading_end

    def is_stock_selection_time(self):
        """检查是否在选股时间内（策略2实时选股）"""
        now = datetime.now()
        if now.weekday() >= 5:
            return False
        # 策略2在交易时间内实时选股
        trading_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
        trading_end = now.replace(hour=14, minute=55, second=0, microsecond=0)
        return trading_start <= now <= trading_end

    def read_stock_pool(self, file_path):
        """读取通达信股票池文件"""
        try:
            if not os.path.exists(file_path):
                logger.warning(f"策略2 - 股票池文件不存在: {file_path}")
                # 返回示例股票池
                return ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']

            # 尝试不同的编码方式读取文件
            encodings = ['gbk', 'utf-8', 'gb2312']
            lines = []

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        lines = f.readlines()
                    break
                except UnicodeDecodeError:
                    continue

            if not lines:
                logger.error(f"策略2 - 无法读取股票池文件: {file_path}")
                return ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']

            stocks = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    # 处理不同格式的股票代码
                    code = None

                    # 格式1: 包含|分隔符
                    if '|' in line:
                        code = line.split('|')[0].strip()
                    # 格式2: 纯数字代码
                    elif line.isdigit():
                        code = line
                    # 格式3: 已经包含后缀的代码
                    elif '.' in line:
                        code = line

                    if code:
                        # 处理7位数字代码，截取为6位
                        if code.isdigit() and len(code) == 7:
                            code = code[1:]  # 去掉第一位

                        # 为6位数字代码添加后缀
                        if code.isdigit() and len(code) == 6:
                            if code.startswith('6'):
                                code = code + '.SH'  # 上海股票
                            elif code.startswith(('0', '3')):
                                code = code + '.SZ'  # 深圳股票

                        # 验证代码格式
                        if '.' in code and len(code) == 9:
                            stocks.append(code)

            logger.info(f"策略2 - 从股票池读取到{len(stocks)}只股票: {stocks[:5]}...")
            return stocks

        except Exception as e:
            logger.error(f"策略2 - 读取股票池文件失败: {e}")
            # 返回示例股票池
            return ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']

    def is_main_board_stock(self, stock_code):
        """判断是否为主板股票"""
        return stock_code.startswith('60') or stock_code.startswith('00')

    def is_limit_up(self, stock_code, current_price, prev_close):
        """判断是否涨停"""
        if self.is_main_board_stock(stock_code):
            limit_up_price = prev_close * 1.1  # 主板10%
        else:
            limit_up_price = prev_close * 1.2  # 创业板20%
        return abs(current_price - limit_up_price) < 0.01

    def calculate_price_change_rate(self, current_price, prev_close):
        """计算涨幅"""
        if prev_close == 0:
            return 0
        return (current_price - prev_close) / prev_close * 100

    def stock_pool_selection(self):
        """股票池选股逻辑（实时执行）"""
        try:
            stock_pool = self.read_stock_pool(self.stock_pool_path)
            selected_stocks = []
            limit_up_stocks = []  # 涨停股票列表，等待打开

            logger.info(f"策略2 - 开始股票池实时选股，股票池包含{len(stock_pool)}只股票")

            for stock_code in stock_pool:
                try:
                    # 获取当前价格和昨收价
                    current_data = xtdata.get_market_data([stock_code], period='tick', count=1)
                    if current_data is None:
                        logger.warning(f"策略2 - 无法获取{stock_code}的行情数据")
                        continue

                    current_price = current_data['lastPrice'][0]
                    prev_close = current_data['preClose'][0]

                    # 计算涨幅
                    price_change_rate = self.calculate_price_change_rate(current_price, prev_close)

                    # 筛选条件1：排除开盘涨幅小于7%的股票
                    if price_change_rate < 7:
                        continue

                    # 筛选条件2：涨幅大于7%且不涨停的股票
                    if price_change_rate >= 7 and not self.is_limit_up(stock_code, current_price, prev_close):
                        selected_stocks.append(stock_code)
                        logger.info(f"策略2 - 选中股票: {stock_code}, 涨幅: {price_change_rate:.2f}%")

                    # 筛选条件3：涨停价开盘的股票，等待打开后监控
                    elif self.is_limit_up(stock_code, current_price, prev_close):
                        limit_up_stocks.append(stock_code)
                        logger.info(f"策略2 - 发现涨停股票{stock_code}，涨幅: {price_change_rate:.2f}%，等待打开后监控")

                except Exception as e:
                    logger.error(f"策略2 - 处理股票{stock_code}时出错: {e}")
                    continue

            # 将涨停股票也加入监控列表（等待打开）
            all_selected = selected_stocks + limit_up_stocks
            logger.info(f"策略2 - 选股完成，普通股票{len(selected_stocks)}只，涨停股票{len(limit_up_stocks)}只")

            return all_selected

        except Exception as e:
            logger.error(f"策略2 - 选股失败: {e}")
            return []

    def check_buy_condition(self, stock_code):
        """检查买入条件：涨幅达到9.96%"""
        try:
            current_data = xtdata.get_market_data([stock_code], period='tick', count=1)
            if current_data is None:
                return False

            current_price = current_data['lastPrice'][0]
            prev_close = current_data['preClose'][0]
            price_change_rate = self.calculate_price_change_rate(current_price, prev_close)

            # 检查是否达到9.96%买入条件
            if price_change_rate >= 9.96:
                # 确保不是涨停状态（涨停无法买入）
                if not self.is_limit_up(stock_code, current_price, prev_close):
                    logger.info(f"策略2 - {stock_code}达到买入条件，涨幅: {price_change_rate:.2f}%")
                    return True
                else:
                    logger.debug(f"策略2 - {stock_code}涨幅{price_change_rate:.2f}%但已涨停，无法买入")

            return False

        except Exception as e:
            logger.error(f"策略2 - 检查买入条件失败 {stock_code}: {e}")
            return False

    def place_buy_order(self, stock_code):
        """下买单（动态半仓）"""
        try:
            # 获取账户资金
            account_info = self.trader.query_stock_asset(self.account)
            if account_info is None:
                logger.error("策略2 - 无法获取账户信息")
                return False

            available_cash = account_info.cash

            # 动态计算仓位：第一只股票用一半资金，第二只股票用剩余资金
            if len(self.bought_stocks) == 0:
                # 第一只股票：用一半资金
                position_cash = available_cash * 0.5
            else:
                # 第二只股票：用剩余资金
                position_cash = available_cash * 0.95  # 留5%作为手续费缓冲

            # 获取当前价格
            current_data = xtdata.get_market_data([stock_code], period='tick', count=1)
            if current_data is None:
                logger.error(f"策略2 - 无法获取{stock_code}当前价格")
                return False

            current_price = current_data['lastPrice'][0]
            buy_quantity = int(position_cash / current_price / 100) * 100

            if buy_quantity < 100:
                logger.warning(f"策略2 - 资金不足，无法买入{stock_code}")
                return False

            # 下买单
            order_id = self.trader.order_stock(
                account=self.account,
                stock_code=stock_code,
                order_type=xtconstant.STOCK_BUY,
                order_volume=buy_quantity,
                price_type=xtconstant.FIX_PRICE,
                price=current_price,
                strategy_name='strategy2_pool',
                order_remark=f'Pool_{stock_code}'
            )

            if order_id > 0:
                logger.info(f"策略2 - 成功下买单: {stock_code}, 数量: {buy_quantity}, 价格: {current_price}, 仓位: {'第一只' if len(self.bought_stocks) == 0 else '第二只'}")
                self.bought_stocks.add(stock_code)
                return True
            else:
                logger.error(f"策略2 - 下买单失败: {stock_code}")
                return False

        except Exception as e:
            logger.error(f"策略2 - 下买单异常 {stock_code}: {e}")
            return False

    def start_trading(self):
        """启动交易策略"""
        self.is_running = True
        logger.info("策略2 - 股票池交易策略已启动")

        # 启动主交易线程
        trading_thread = threading.Thread(target=self.main_trading_loop)
        trading_thread.daemon = True
        trading_thread.start()

    def main_trading_loop(self):
        """主交易循环"""
        last_selection_time = None
        selection_interval = 180  # 3分钟重新选股一次

        while self.is_running:
            try:
                current_time = datetime.now()

                # 交易时间内实时选股和监控
                if self.is_trading_time():
                    # 实时选股逻辑：每3分钟重新选股一次
                    if (last_selection_time is None or
                        (current_time - last_selection_time).total_seconds() >= selection_interval):

                        logger.info("策略2 - 开始股票池实时选股...")
                        new_selected_stocks = self.stock_pool_selection()

                        # 更新选股列表（股票池相对固定，直接替换）
                        self.selected_stocks = new_selected_stocks

                        last_selection_time = current_time
                        logger.info(f"策略2 - 选股完成，当前监控{len(self.selected_stocks)}只股票")

                    # 监控买入条件
                    for stock_code in self.selected_stocks[:]:  # 使用切片避免迭代时修改列表
                        if stock_code not in self.bought_stocks:
                            if self.check_buy_condition(stock_code):
                                if self.place_buy_order(stock_code):
                                    # 买入成功后，如果已买入2只股票，停止监控
                                    if len(self.bought_stocks) >= 2:
                                        logger.info("策略2 - 已买入2只股票，停止监控")
                                        break

                time.sleep(3)  # 3秒检查一次

            except Exception as e:
                logger.error(f"策略2 - 主交易循环异常: {e}")
                time.sleep(10)

    def run_strategy(self):
        """运行策略"""
        logger.info("策略2 - 启动股票池自动交易策略...")
        logger.info("策略2 - 注意：本策略在9:30开盘后进行选股")
        self.start_trading()

        try:
            while self.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("策略2 - 接收到退出信号")
            self.is_running = False
            self.trader.unsubscribe(self.account)
            self.trader.stop()


if __name__ == "__main__":
    # 配置参数
    ACCOUNT_ID = "**********"  # 您的资金账号
    STOCK_POOL_PATH = r"D:\tdx\T0002\blocknew\1J2.blk"  # 通达信股票池路径

    try:
        # 创建并运行策略2
        strategy2 = Strategy2_StockPool_Trading(ACCOUNT_ID, STOCK_POOL_PATH)
        strategy2.run_strategy()

    except Exception as e:
        logger.error(f"策略2 - 程序运行异常: {e}")
        print(f"策略2错误: {e}")
        print("请检查:")
        print("1. QMT客户端是否已启动并登录")
        print("2. 股票池文件路径是否正确")
        print("3. 通达信股票池文件是否存在")