# -*- coding: UTF-8 -*-
"""
@Project ：stock_code 
@File    ：策略2_股票池选股.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/9 15:41:45
"""
# -*- coding: utf-8 -*-
"""
策略2：股票池选股自动交易
从通达信股票池筛选涨幅大于7%且不涨停的股票，涨幅达到9.96%时自动买入半仓
修正：在9:30开盘后进行选股，而非9:25-9:30
"""

import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
import os
from xtquant import xtdata
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount
from xtquant import xtconstant
import threading
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class Strategy2Callback(XtQuantTraderCallback):
    """策略2回调类"""

    def on_disconnected(self):
        logger.warning("策略2 - QMT连接断开")

    def on_stock_order(self, order):
        logger.info(f"策略2 - 委托回调: {order.stock_code}, 状态: {order.order_status}")

    def on_stock_trade(self, trade):
        logger.info(f"策略2 - 成交回调: {trade.stock_code}, 数量: {trade.traded_volume}")

    def on_order_error(self, order_error):
        logger.error(f"策略2 - 委托失败: {order_error.error_msg}")


class Strategy2_StockPool_Trading:
    def __init__(self, account_id, stock_pool_path=None, mini_qmt_path=None):
        """
        初始化策略2：股票池选股策略
        """
        self.account_id = account_id
        self.session_id = int(time.time()) + 1  # 避免与策略1冲突

        # 股票池路径
        if stock_pool_path is None:
            self.stock_pool_path = r"D:\tdx\T0002\blocknew\1J2.blk"
        else:
            self.stock_pool_path = stock_pool_path

        # 设置MiniQMT路径
        if mini_qmt_path is None:
            possible_paths = [
                r'D:\国金证券QMT交易端\userdata_mini',
                r'D:\国金证券QMT交易端\bin.x64\userdata_mini',
                r'D:\国金证券QMT交易端\bin\userdata_mini'
            ]

            mini_qmt_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    mini_qmt_path = path
                    logger.info(f"策略2 - 找到userdata_mini路径: {path}")
                    break

            if mini_qmt_path is None:
                raise Exception("策略2 - 未找到userdata_mini目录")

        try:
            # 初始化QMT连接
            self.trader = XtQuantTrader(mini_qmt_path, self.session_id)
            self.account = StockAccount(account_id)

            # 注册回调
            self.callback = Strategy2Callback()
            self.trader.register_callback(self.callback)

            # 启动并连接
            logger.info("策略2 - 启动QMT交易客户端...")
            self.trader.start()

            logger.info("策略2 - 建立QMT连接...")
            connect_result = self.trader.connect()
            if connect_result == 0:
                logger.info("策略2 - QMT连接成功")
            else:
                raise Exception(f"策略2 - QMT连接失败，错误码: {connect_result}")

            # 订阅账户
            self.trader.subscribe(self.account)

        except Exception as e:
            logger.error(f"策略2 - 初始化失败: {e}")
            raise

        # 策略状态
        self.is_running = False
        self.selected_stocks = []
        self.bought_stocks = set()
        self.selection_completed = False  # 新增：标记是否已完成选股

    def is_trading_time(self):
        """检查是否在交易时间内"""
        now = datetime.now()
        if now.weekday() >= 5:  # 跳过周末
            return False
        trading_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
        trading_end = now.replace(hour=14, minute=55, second=0, microsecond=0)
        return trading_start <= now <= trading_end

    def is_stock_selection_time(self):
        """检查是否在选股时间内（策略2在9:30开盘后选股）"""
        now = datetime.now()
        if now.weekday() >= 5:
            return False
        # 策略2在9:30开盘后选股
        selection_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
        selection_end = now.replace(hour=9, minute=35, second=0, microsecond=0)  # 给5分钟时间完成选股
        return selection_start <= now <= selection_end

    def read_stock_pool(self, file_path):
        """读取通达信股票池文件"""
        try:
            if not os.path.exists(file_path):
                logger.warning(f"策略2 - 股票池文件不存在: {file_path}")
                # 返回示例股票池
                return ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']

            with open(file_path, 'r', encoding='gbk') as f:
                lines = f.readlines()

            stocks = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    # 解析股票代码
                    if '|' in line:
                        code = line.split('|')[0].strip()
                        stocks.append(code)

            logger.info(f"策略2 - 从股票池读取到{len(stocks)}只股票")
            return stocks

        except Exception as e:
            logger.error(f"策略2 - 读取股票池文件失败: {e}")
            # 返回示例股票池
            return ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']

    def is_main_board_stock(self, stock_code):
        """判断是否为主板股票"""
        return stock_code.startswith('60') or stock_code.startswith('00')

    def is_limit_up(self, stock_code, current_price, prev_close):
        """判断是否涨停"""
        if self.is_main_board_stock(stock_code):
            limit_up_price = prev_close * 1.1  # 主板10%
        else:
            limit_up_price = prev_close * 1.2  # 创业板20%
        return abs(current_price - limit_up_price) < 0.01

    def calculate_price_change_rate(self, current_price, prev_close):
        """计算涨幅"""
        if prev_close == 0:
            return 0
        return (current_price - prev_close) / prev_close * 100

    def stock_pool_selection(self):
        """股票池选股逻辑（开盘后执行）"""
        try:
            stock_pool = self.read_stock_pool(self.stock_pool_path)
            selected_stocks = []

            logger.info(f"策略2 - 开始股票池选股（开盘后），股票池包含{len(stock_pool)}只股票")

            for stock_code in stock_pool:
                try:
                    # 获取当前价格和昨收价
                    current_data = xtdata.get_market_data([stock_code], period='tick', count=1)
                    if current_data is None:
                        continue

                    current_price = current_data['lastPrice'][0]
                    prev_close = current_data['preClose'][0]

                    # 计算涨幅
                    price_change_rate = self.calculate_price_change_rate(current_price, prev_close)

                    # 筛选条件：涨幅大于7%且不涨停
                    if price_change_rate > 7 and not self.is_limit_up(stock_code, current_price, prev_close):
                        selected_stocks.append(stock_code)
                        logger.info(f"策略2 - 选中股票: {stock_code}, 涨幅: {price_change_rate:.2f}%")

                    # 处理涨停一字板开盘的情况
                    elif self.is_limit_up(stock_code, current_price, prev_close):
                        # 记录涨停股票，等待打开后监控
                        logger.info(f"策略2 - 发现涨停股票{stock_code}，等待打开后监控")

                except Exception as e:
                    logger.error(f"策略2 - 处理股票{stock_code}时出错: {e}")
                    continue

            return selected_stocks

        except Exception as e:
            logger.error(f"策略2 - 选股失败: {e}")
            return []

    def check_buy_condition(self, stock_code):
        """检查买入条件：涨幅达到9.96%"""
        try:
            current_data = xtdata.get_market_data([stock_code], period='tick', count=1)
            if current_data is None:
                return False

            current_price = current_data['lastPrice'][0]
            prev_close = current_data['preClose'][0]
            price_change_rate = self.calculate_price_change_rate(current_price, prev_close)

            # 检查是否达到9.96%买入条件
            if price_change_rate >= 9.96:
                # 额外检查：如果之前是涨停的，确保已经打开
                if not self.is_limit_up(stock_code, current_price, prev_close):
                    return True

            return False

        except Exception as e:
            logger.error(f"策略2 - 检查买入条件失败 {stock_code}: {e}")
            return False

    def place_buy_order(self, stock_code):
        """下买单（半仓）"""
        try:
            # 获取账户资金
            account_info = self.trader.query_stock_asset(self.account)
            if account_info is None:
                logger.error("策略2 - 无法获取账户信息")
                return False

            available_cash = account_info.cash
            half_cash = available_cash * 0.5  # 半仓

            # 获取当前价格
            current_data = xtdata.get_market_data([stock_code], period='tick', count=1)
            if current_data is None:
                logger.error(f"策略2 - 无法获取{stock_code}当前价格")
                return False

            current_price = current_data['lastPrice'][0]
            buy_quantity = int(half_cash / current_price / 100) * 100

            if buy_quantity < 100:
                logger.warning(f"策略2 - 资金不足，无法买入{stock_code}")
                return False

            # 下买单
            order_id = self.trader.order_stock(
                account=self.account,
                stock_code=stock_code,
                order_type=xtconstant.STOCK_BUY,
                order_volume=buy_quantity,
                price_type=xtconstant.FIX_PRICE,
                price=current_price,
                strategy_name='strategy2_pool',
                order_remark=f'Pool_{stock_code}'
            )

            if order_id > 0:
                logger.info(f"策略2 - 成功下买单: {stock_code}, 数量: {buy_quantity}, 价格: {current_price}")
                self.bought_stocks.add(stock_code)
                return True
            else:
                logger.error(f"策略2 - 下买单失败: {stock_code}")
                return False

        except Exception as e:
            logger.error(f"策略2 - 下买单异常 {stock_code}: {e}")
            return False

    def start_trading(self):
        """启动交易策略"""
        self.is_running = True
        logger.info("策略2 - 股票池交易策略已启动")

        # 启动主交易线程
        trading_thread = threading.Thread(target=self.main_trading_loop)
        trading_thread.daemon = True
        trading_thread.start()

    def main_trading_loop(self):
        """主交易循环"""
        last_selection_date = None

        while self.is_running:
            try:
                current_time = datetime.now()
                today = current_time.date()

                # 每天重置选股状态
                if last_selection_date != today:
                    self.selection_completed = False

                # 选股时间：9:30-9:35（开盘后选股，每天只选一次）
                if (self.is_stock_selection_time() and
                        last_selection_date != today and
                        not self.selection_completed):

                    logger.info("策略2 - 开始股票池选股（开盘后选股）...")
                    self.selected_stocks = self.stock_pool_selection()
                    last_selection_date = today
                    self.selection_completed = True
                    logger.info(f"策略2 - 选股完成，共选中{len(self.selected_stocks)}只股票")

                # 交易时间：9:30-14:55（选股完成后开始监控买入）
                elif self.is_trading_time() and self.selection_completed:
                    for stock_code in self.selected_stocks:
                        if stock_code not in self.bought_stocks:
                            if self.check_buy_condition(stock_code):
                                self.place_buy_order(stock_code)

                time.sleep(5)  # 5秒检查一次

            except Exception as e:
                logger.error(f"策略2 - 主交易循环异常: {e}")
                time.sleep(10)

    def run_strategy(self):
        """运行策略"""
        logger.info("策略2 - 启动股票池自动交易策略...")
        logger.info("策略2 - 注意：本策略在9:30开盘后进行选股")
        self.start_trading()

        try:
            while self.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("策略2 - 接收到退出信号")
            self.is_running = False
            self.trader.unsubscribe(self.account)
            self.trader.stop()


if __name__ == "__main__":
    # 配置参数
    ACCOUNT_ID = "**********"  # 您的资金账号
    STOCK_POOL_PATH = r"D:\tdx\T0002\blocknew\1J2.blk"  # 通达信股票池路径

    try:
        # 创建并运行策略2
        strategy2 = Strategy2_StockPool_Trading(ACCOUNT_ID, STOCK_POOL_PATH)
        strategy2.run_strategy()

    except Exception as e:
        logger.error(f"策略2 - 程序运行异常: {e}")
        print(f"策略2错误: {e}")
        print("请检查:")
        print("1. QMT客户端是否已启动并登录")
        print("2. 股票池文件路径是否正确")
        print("3. 通达信股票池文件是否存在")