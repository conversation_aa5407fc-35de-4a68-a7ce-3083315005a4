# -*- coding: UTF-8 -*-
"""
@Project ：stock_code 
@File    ：策略2_股票池选股.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/9 15:41:45
"""
# -*- coding: utf-8 -*-
"""
策略2：股票池选股自动交易
从通达信股票池筛选涨幅大于7%且不涨停的股票，涨幅达到9.96%时自动买入半仓
修正：在9:30开盘后进行选股，而非9:25-9:30
"""

import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
import os
from xtquant import xtdata
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount
from xtquant import xtconstant
import threading
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class Strategy2Callback(XtQuantTraderCallback):
    """策略2回调类"""

    def on_disconnected(self):
        logger.warning("策略2 - QMT连接断开")

    def on_stock_order(self, order):
        logger.info(f"策略2 - 委托回调: {order.stock_code}, 状态: {order.order_status}")

    def on_stock_trade(self, trade):
        logger.info(f"策略2 - 成交回调: {trade.stock_code}, 数量: {trade.traded_volume}")

    def on_order_error(self, order_error):
        logger.error(f"策略2 - 委托失败: {order_error.error_msg}")


class Strategy2_StockPool_Trading:
    def __init__(self, account_id, stock_pool_path=None, mini_qmt_path=None):
        """
        初始化策略2：股票池选股策略
        """
        self.account_id = account_id
        self.session_id = int(time.time()) + 1  # 避免与策略1冲突

        # 股票池路径
        if stock_pool_path is None:
            self.stock_pool_path = r"D:\tdx\T0002\blocknew\1J2.blk"
        else:
            self.stock_pool_path = stock_pool_path

        # 设置MiniQMT路径
        if mini_qmt_path is None:
            possible_paths = [
                r'D:\国金证券QMT交易端\userdata_mini',
                r'D:\国金证券QMT交易端\bin.x64\userdata_mini',
                r'D:\国金证券QMT交易端\bin\userdata_mini'
            ]

            mini_qmt_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    mini_qmt_path = path
                    logger.info(f"策略2 - 找到userdata_mini路径: {path}")
                    break

            if mini_qmt_path is None:
                raise Exception("策略2 - 未找到userdata_mini目录")

        try:
            # 初始化QMT连接
            self.trader = XtQuantTrader(mini_qmt_path, self.session_id)
            self.account = StockAccount(account_id)

            # 注册回调
            self.callback = Strategy2Callback()
            self.trader.register_callback(self.callback)

            # 启动并连接
            logger.info("策略2 - 启动QMT交易客户端...")
            self.trader.start()

            logger.info("策略2 - 建立QMT连接...")
            connect_result = self.trader.connect()
            if connect_result == 0:
                logger.info("策略2 - QMT连接成功")
            else:
                raise Exception(f"策略2 - QMT连接失败，错误码: {connect_result}")

            # 订阅账户
            self.trader.subscribe(self.account)

        except Exception as e:
            logger.error(f"策略2 - 初始化失败: {e}")
            raise

        # 策略状态
        self.is_running = False
        self.selected_stocks = []
        self.bought_stocks = set()
        self.selection_completed = False  # 新增：标记是否已完成选股

    def is_trading_time(self):
        """检查是否在交易时间内"""
        now = datetime.now()
        if now.weekday() >= 5:  # 跳过周末
            return False
        trading_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
        trading_end = now.replace(hour=14, minute=55, second=0, microsecond=0)
        return trading_start <= now <= trading_end

    def is_stock_selection_time(self):
        """检查是否在选股时间内（策略2实时选股）"""
        now = datetime.now()
        if now.weekday() >= 5:
            return False
        # 策略2在交易时间内实时选股
        trading_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
        trading_end = now.replace(hour=14, minute=55, second=0, microsecond=0)
        return trading_start <= now <= trading_end

    def read_stock_pool(self, file_path):
        """读取通达信股票池文件"""
        try:
            if not os.path.exists(file_path):
                logger.warning(f"策略2 - 股票池文件不存在: {file_path}")
                logger.warning("策略2 - 请检查股票池文件路径是否正确")
                logger.warning("策略2 - 使用示例股票池进行测试")
                return ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']

            # 尝试不同的编码方式读取文件
            encodings = ['gbk', 'utf-8', 'gb2312', 'ansi']
            lines = []
            used_encoding = None

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        lines = f.readlines()
                    used_encoding = encoding
                    logger.info(f"策略2 - 使用{encoding}编码成功读取股票池文件")
                    break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    logger.warning(f"策略2 - 使用{encoding}编码读取失败: {e}")
                    continue

            if not lines:
                logger.error(f"策略2 - 无法读取股票池文件: {file_path}")
                logger.error("策略2 - 尝试了所有编码方式都失败")
                return ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']

            stocks = []
            processed_codes = set()  # 用于去重

            logger.info(f"策略2 - 开始处理股票池文件，共{len(lines)}行数据")

            for line_num, line in enumerate(lines, 1):
                original_line = line
                line = line.strip()

                if not line or line.startswith('#'):
                    continue

                # 处理不同格式的股票代码
                code = None

                # 格式1: 包含|分隔符 (如: 1000001|平安银行)
                if '|' in line:
                    parts = line.split('|')
                    code = parts[0].strip()
                    stock_name = parts[1].strip() if len(parts) > 1 else ""
                    logger.debug(f"策略2 - 第{line_num}行: 分隔符格式 {code}|{stock_name}")

                # 格式2: 纯数字代码
                elif line.isdigit():
                    code = line
                    logger.debug(f"策略2 - 第{line_num}行: 纯数字格式 {code}")

                # 格式3: 已经包含后缀的代码
                elif '.' in line and len(line) == 9:
                    code = line
                    logger.debug(f"策略2 - 第{line_num}行: 完整格式 {code}")

                # 格式4: 其他可能的格式
                else:
                    # 尝试提取数字部分
                    import re
                    numbers = re.findall(r'\d+', line)
                    if numbers:
                        code = numbers[0]
                        logger.debug(f"策略2 - 第{line_num}行: 提取数字 {code} from {line}")

                if code:
                    original_code = code

                    # 处理7位数字代码，截取为6位
                    if code.isdigit() and len(code) == 7:
                        code = code[1:]  # 去掉第一位
                        logger.info(f"策略2 - 第{line_num}行: 7位代码转换 {original_code} -> {code}")

                    # 为6位数字代码添加后缀
                    if code.isdigit() and len(code) == 6:
                        if code.startswith('6'):
                            code = code + '.SH'  # 上海股票
                        elif code.startswith(('0', '3')):
                            code = code + '.SZ'  # 深圳股票
                        else:
                            logger.warning(f"策略2 - 第{line_num}行: 未知市场代码 {code}")
                            continue
                        logger.info(f"策略2 - 第{line_num}行: 添加后缀 {code}")

                    # 验证代码格式并去重
                    if '.' in code and len(code) == 9:
                        if code not in processed_codes:
                            stocks.append(code)
                            processed_codes.add(code)
                        else:
                            logger.debug(f"策略2 - 第{line_num}行: 重复股票代码 {code}，已跳过")
                    else:
                        logger.warning(f"策略2 - 第{line_num}行: 无效代码格式 {code}")
                else:
                    logger.warning(f"策略2 - 第{line_num}行: 无法解析 '{original_line.strip()}'")

            logger.info(f"策略2 - 股票池处理完成:")
            logger.info(f"  - 文件路径: {file_path}")
            logger.info(f"  - 使用编码: {used_encoding}")
            logger.info(f"  - 原始行数: {len(lines)}")
            logger.info(f"  - 有效股票: {len(stocks)}只")
            logger.info(f"  - 股票列表: {stocks[:10]}{'...' if len(stocks) > 10 else ''}")

            if len(stocks) == 0:
                logger.error("策略2 - 未能解析出任何有效股票代码")
                return ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']

            return stocks

        except Exception as e:
            logger.error(f"策略2 - 读取股票池文件异常: {e}")
            import traceback
            logger.error(f"策略2 - 详细错误信息: {traceback.format_exc()}")
            # 返回示例股票池
            return ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']

    def is_main_board_stock(self, stock_code):
        """判断是否为主板股票"""
        return stock_code.startswith('60') or stock_code.startswith('00')

    def is_limit_up(self, stock_code, current_price, prev_close):
        """判断是否涨停"""
        if self.is_main_board_stock(stock_code):
            limit_up_price = prev_close * 1.1  # 主板10%
        else:
            limit_up_price = prev_close * 1.2  # 创业板20%
        return abs(current_price - limit_up_price) < 0.01

    def calculate_price_change_rate(self, current_price, prev_close):
        """计算涨幅"""
        if prev_close == 0:
            return 0
        return (current_price - prev_close) / prev_close * 100

    def stock_pool_selection(self):
        """股票池选股逻辑（实时执行）
        需求：
        1. 排除开盘涨幅小于7%的股票
        2. 保留开盘涨幅大于7%且不涨停的股票
        3. 保留涨停价开盘的股票，等待打开后监控
        """
        try:
            stock_pool = self.read_stock_pool(self.stock_pool_path)
            selected_stocks = []      # 涨幅7%以上且不涨停的股票
            limit_up_stocks = []      # 涨停股票，等待打开
            excluded_stocks = []      # 被排除的股票
            error_stocks = []         # 数据获取失败的股票

            logger.info(f"策略2 - 开始股票池实时选股，股票池包含{len(stock_pool)}只股票")
            logger.info("策略2 - 筛选条件：排除涨幅<7%，保留涨幅≥7%且不涨停，监控涨停股票")

            for stock_code in stock_pool:
                try:
                    # 获取当前价格和昨收价
                    current_data = xtdata.get_market_data([stock_code], period='tick', count=1)
                    if current_data is None:
                        logger.warning(f"策略2 - 无法获取{stock_code}的行情数据")
                        error_stocks.append(stock_code)
                        continue

                    current_price = current_data['lastPrice'][0]
                    prev_close = current_data['preClose'][0]

                    # 数据有效性检查
                    if current_price <= 0 or prev_close <= 0:
                        logger.warning(f"策略2 - {stock_code}价格数据异常: 现价{current_price}, 昨收{prev_close}")
                        error_stocks.append(stock_code)
                        continue

                    # 计算涨幅
                    price_change_rate = self.calculate_price_change_rate(current_price, prev_close)

                    logger.debug(f"策略2 - 分析{stock_code}: 昨收{prev_close:.2f}, 现价{current_price:.2f}, 涨幅{price_change_rate:.2f}%")

                    # 筛选条件1：排除开盘涨幅小于7%的股票
                    if price_change_rate < 7:
                        excluded_stocks.append((stock_code, price_change_rate, "涨幅小于7%"))
                        logger.debug(f"策略2 - 排除{stock_code}: 涨幅{price_change_rate:.2f}% < 7%")
                        continue

                    # 检查是否涨停
                    is_limit_up = self.is_limit_up(stock_code, current_price, prev_close)

                    # 筛选条件2：涨幅大于7%且不涨停的股票
                    if price_change_rate >= 7 and not is_limit_up:
                        selected_stocks.append(stock_code)
                        logger.info(f"策略2 - ✓ 选中股票: {stock_code}, 涨幅: {price_change_rate:.2f}%")

                    # 筛选条件3：涨停价开盘的股票，等待打开后监控
                    elif is_limit_up:
                        limit_up_stocks.append(stock_code)
                        logger.info(f"策略2 - 📈 涨停股票: {stock_code}, 涨幅: {price_change_rate:.2f}%，等待打开后监控")

                except Exception as e:
                    logger.error(f"策略2 - 处理股票{stock_code}时出错: {e}")
                    error_stocks.append(stock_code)
                    continue

            # 将涨停股票也加入监控列表（等待打开）
            all_selected = selected_stocks + limit_up_stocks

            # 详细的选股结果统计
            logger.info(f"策略2 - 选股完成，详细统计:")
            logger.info(f"  📊 股票池总数: {len(stock_pool)}只")
            logger.info(f"  ✅ 普通选中: {len(selected_stocks)}只 (涨幅≥7%且不涨停)")
            logger.info(f"  📈 涨停等待: {len(limit_up_stocks)}只 (等待打开后监控)")
            logger.info(f"  ❌ 排除股票: {len(excluded_stocks)}只 (涨幅<7%)")
            logger.info(f"  ⚠️  数据异常: {len(error_stocks)}只")
            logger.info(f"  🎯 总监控数: {len(all_selected)}只")

            # 显示选中的股票列表
            if selected_stocks:
                logger.info(f"  普通选中股票: {selected_stocks}")
            if limit_up_stocks:
                logger.info(f"  涨停等待股票: {limit_up_stocks}")

            # 如果没有选中任何股票，给出建议
            if len(all_selected) == 0:
                logger.warning("策略2 - 未选中任何股票，可能原因:")
                logger.warning("1. 股票池中所有股票涨幅都小于7%")
                logger.warning("2. 数据获取失败")
                logger.warning("3. 当前不在交易时间")
                logger.warning("建议检查股票池和市场状态")

            return all_selected

        except Exception as e:
            logger.error(f"策略2 - 选股失败: {e}")
            import traceback
            logger.error(f"策略2 - 详细错误: {traceback.format_exc()}")
            return []

    def check_buy_condition(self, stock_code):
        """检查买入条件：涨幅达到9.96%
        特殊处理：
        1. 如果是涨停价开盘的股票，需要等待打开涨停后，涨幅重新达到9.96%才买入
        2. 如果是普通股票，涨幅达到9.96%且不涨停时买入
        """
        try:
            current_data = xtdata.get_market_data([stock_code], period='tick', count=1)
            if current_data is None:
                logger.debug(f"策略2 - {stock_code}无法获取实时数据")
                return False

            current_price = current_data['lastPrice'][0]
            prev_close = current_data['preClose'][0]

            # 数据有效性检查
            if current_price <= 0 or prev_close <= 0:
                logger.warning(f"策略2 - {stock_code}价格数据异常: 现价{current_price}, 昨收{prev_close}")
                return False

            price_change_rate = self.calculate_price_change_rate(current_price, prev_close)
            is_limit_up = self.is_limit_up(stock_code, current_price, prev_close)

            # 详细的买入条件检查日志
            logger.debug(f"策略2 - 买入检查{stock_code}: 涨幅{price_change_rate:.2f}%, 涨停{is_limit_up}")

            # 检查是否达到9.96%买入条件
            if price_change_rate >= 9.96:
                # 确保不是涨停状态（涨停无法买入）
                if not is_limit_up:
                    logger.info(f"策略2 - 🎯 {stock_code}达到买入条件！涨幅: {price_change_rate:.2f}%，非涨停状态")
                    return True
                else:
                    logger.debug(f"策略2 - {stock_code}涨幅{price_change_rate:.2f}%已达标但涨停中，无法买入")
                    return False
            else:
                # 如果涨幅不足9.96%，记录当前状态
                if price_change_rate >= 9.0:  # 接近买入条件时记录
                    logger.debug(f"策略2 - {stock_code}涨幅{price_change_rate:.2f}%接近买入条件(9.96%)")
                return False

        except Exception as e:
            logger.error(f"策略2 - 检查买入条件失败 {stock_code}: {e}")
            return False

    def place_buy_order(self, stock_code):
        """下买单（动态半仓）"""
        try:
            # 获取账户资金
            account_info = self.trader.query_stock_asset(self.account)
            if account_info is None:
                logger.error("策略2 - 无法获取账户信息")
                return False

            available_cash = account_info.cash

            # 动态计算仓位：第一只股票用一半资金，第二只股票用剩余资金
            if len(self.bought_stocks) == 0:
                # 第一只股票：用一半资金
                position_cash = available_cash * 0.5
            else:
                # 第二只股票：用剩余资金
                position_cash = available_cash * 0.95  # 留5%作为手续费缓冲

            # 获取当前价格
            current_data = xtdata.get_market_data([stock_code], period='tick', count=1)
            if current_data is None:
                logger.error(f"策略2 - 无法获取{stock_code}当前价格")
                return False

            current_price = current_data['lastPrice'][0]
            buy_quantity = int(position_cash / current_price / 100) * 100

            if buy_quantity < 100:
                logger.warning(f"策略2 - 资金不足，无法买入{stock_code}")
                return False

            # 下买单
            order_id = self.trader.order_stock(
                account=self.account,
                stock_code=stock_code,
                order_type=xtconstant.STOCK_BUY,
                order_volume=buy_quantity,
                price_type=xtconstant.FIX_PRICE,
                price=current_price,
                strategy_name='strategy2_pool',
                order_remark=f'Pool_{stock_code}'
            )

            if order_id > 0:
                logger.info(f"策略2 - 成功下买单: {stock_code}, 数量: {buy_quantity}, 价格: {current_price}, 仓位: {'第一只' if len(self.bought_stocks) == 0 else '第二只'}")
                self.bought_stocks.add(stock_code)
                return True
            else:
                logger.error(f"策略2 - 下买单失败: {stock_code}")
                return False

        except Exception as e:
            logger.error(f"策略2 - 下买单异常 {stock_code}: {e}")
            return False

    def start_trading(self):
        """启动交易策略"""
        self.is_running = True
        logger.info("策略2 - 股票池交易策略已启动")

        # 启动主交易线程
        trading_thread = threading.Thread(target=self.main_trading_loop)
        trading_thread.daemon = True
        trading_thread.start()

    def main_trading_loop(self):
        """主交易循环
        实现需求：
        1. 实时监控筛选后的股票池
        2. 排除开盘涨幅小于7%的股票
        3. 监控涨幅大于7%且不涨停的股票，涨幅达到9.96%时买入半仓
        4. 监控涨停价开盘的股票，打开涨停后涨幅重新达到9.96%时买入半仓
        """
        last_selection_time = None
        selection_interval = 180  # 3分钟重新选股一次
        last_monitor_time = None
        monitor_interval = 5  # 5秒监控一次买入条件

        logger.info("策略2 - 启动主交易循环，开始实时监控...")

        while self.is_running:
            try:
                current_time = datetime.now()

                # 交易时间内实时选股和监控
                if self.is_trading_time():
                    # 实时选股逻辑：每3分钟重新选股一次
                    if (last_selection_time is None or
                        (current_time - last_selection_time).total_seconds() >= selection_interval):

                        logger.info("策略2 - 🔄 开始股票池实时选股...")
                        new_selected_stocks = self.stock_pool_selection()

                        # 更新选股列表（股票池相对固定，直接替换）
                        old_count = len(self.selected_stocks)
                        self.selected_stocks = new_selected_stocks

                        last_selection_time = current_time
                        logger.info(f"策略2 - ✅ 选股更新完成，监控股票: {old_count} -> {len(self.selected_stocks)}只")

                    # 监控买入条件：每5秒检查一次
                    if (last_monitor_time is None or
                        (current_time - last_monitor_time).total_seconds() >= monitor_interval):

                        if len(self.selected_stocks) > 0 and len(self.bought_stocks) < 2:
                            logger.debug(f"策略2 - 🔍 监控买入条件，当前监控{len(self.selected_stocks)}只股票，已买入{len(self.bought_stocks)}只")

                            for stock_code in self.selected_stocks[:]:  # 使用切片避免迭代时修改列表
                                if stock_code not in self.bought_stocks:
                                    if self.check_buy_condition(stock_code):
                                        logger.info(f"策略2 - 🚀 准备买入{stock_code}")
                                        if self.place_buy_order(stock_code):
                                            # 买入成功后，如果已买入2只股票，停止监控
                                            if len(self.bought_stocks) >= 2:
                                                logger.info("策略2 - 🎯 已买入2只股票，停止监控")
                                                self.is_running = False  # 停止策略运行
                                                break

                        last_monitor_time = current_time

                else:
                    # 非交易时间
                    if current_time.hour < 9 or current_time.hour >= 15:
                        logger.debug("策略2 - 非交易时间，等待开盘...")
                    time.sleep(30)  # 非交易时间延长等待

                time.sleep(2)  # 2秒检查一次

            except KeyboardInterrupt:
                logger.info("策略2 - 接收到停止信号")
                self.is_running = False
                break
            except Exception as e:
                logger.error(f"策略2 - 主交易循环异常: {e}")
                import traceback
                logger.error(f"策略2 - 详细错误: {traceback.format_exc()}")
                time.sleep(10)

    def run_strategy(self):
        """运行策略"""
        logger.info("策略2 - 启动股票池自动交易策略...")
        logger.info("策略2 - 注意：本策略在9:30开盘后进行选股")
        self.start_trading()

        try:
            while self.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("策略2 - 接收到退出信号")
            self.is_running = False
            self.trader.unsubscribe(self.account)
            self.trader.stop()


if __name__ == "__main__":
    # 配置参数
    ACCOUNT_ID = "**********"  # 您的资金账号
    STOCK_POOL_PATH = r"D:\tdx\T0002\blocknew\1J2.blk"  # 通达信股票池路径

    try:
        # 创建并运行策略2
        strategy2 = Strategy2_StockPool_Trading(ACCOUNT_ID, STOCK_POOL_PATH)
        strategy2.run_strategy()

    except Exception as e:
        logger.error(f"策略2 - 程序运行异常: {e}")
        print(f"策略2错误: {e}")
        print("请检查:")
        print("1. QMT客户端是否已启动并登录")
        print("2. 股票池文件路径是否正确")
        print("3. 通达信股票池文件是否存在")