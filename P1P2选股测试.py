# -*- coding: utf-8 -*-
"""
P1P2选股测试脚本 - 不依赖QMT，使用模拟数据测试选股逻辑
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
import random

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def generate_test_stock_data(stock_code, days=10):
    """生成测试用的股票数据"""
    base_price = random.uniform(8, 50)  # 基础价格
    
    closes = []
    opens = []
    
    for i in range(days):
        # 生成一些随机但合理的价格数据
        if i == 0:
            close = base_price
            open_price = base_price * random.uniform(0.98, 1.02)
        else:
            # 基于前一日收盘价生成新价格
            prev_close = closes[-1]
            change_rate = random.uniform(-0.05, 0.05)  # -5%到+5%的变化
            close = prev_close * (1 + change_rate)
            open_price = close * random.uniform(0.98, 1.02)
        
        closes.append(close)
        opens.append(open_price)
    
    return {
        'close': np.array(closes),
        'open': np.array(opens)
    }


def calculate_p1p2(closes, opens):
    """计算P1P2指标"""
    if len(closes) < 4 or len(opens) < 4:
        return None, None, None, None
    
    # 使用倒数第二天作为"当前"（模拟完整交易日）
    current_close = closes[-2]
    current_open = opens[-2]
    
    # REF(O,1) 和 REF(O,2)
    ref_open_1 = opens[-3] if len(opens) >= 3 else opens[-2]
    ref_open_2 = opens[-4] if len(opens) >= 4 else opens[-3]
    
    # 前一日数据
    prev_close = closes[-3] if len(closes) >= 3 else closes[-2]
    prev_open = opens[-3] if len(opens) >= 3 else opens[-2]
    prev_ref_open_1 = opens[-4] if len(opens) >= 4 else opens[-3]
    prev_ref_open_2 = opens[-5] if len(opens) >= 5 else opens[-4]
    
    # 计算P1指标
    p1_current = 1 if (current_close < ref_open_1 and current_close < current_open) else 0
    p1_prev = 1 if (prev_close < prev_ref_open_1 and prev_close < prev_open) else 0
    
    # 计算P2指标
    p2_current = 1 if (current_close < ref_open_2 and current_close < current_open) else 0
    p2_prev = 1 if (prev_close < prev_ref_open_2 and prev_close < prev_open) else 0
    
    return p1_current, p1_prev, p2_current, p2_prev


def test_p1p2_selection_with_random_data():
    """使用随机数据测试P1P2选股"""
    print("="*60)
    print("使用随机数据测试P1P2选股")
    print("="*60)
    
    # 生成大量测试股票
    test_stocks = []
    for i in range(1000):  # 生成1000只测试股票
        stock_code = f"TEST{i:04d}.SH" if i % 2 == 0 else f"TEST{i:04d}.SZ"
        test_stocks.append(stock_code)
    
    selected_stocks = []
    processed_count = 0
    condition1_count = 0
    condition2_count = 0
    
    for stock_code in test_stocks:
        try:
            processed_count += 1
            
            # 生成测试数据
            data = generate_test_stock_data(stock_code, days=10)
            closes = data['close']
            opens = data['open']
            
            # 计算P1P2
            p1_current, p1_prev, p2_current, p2_prev = calculate_p1p2(closes, opens)
            
            if p1_current is None:
                continue
            
            # 选股条件
            condition1 = p1_current == 0 and p1_prev == 1
            condition2 = p2_current == 0 and p2_prev == 1
            
            if condition1:
                condition1_count += 1
            if condition2:
                condition2_count += 1
            
            # 记录前几只股票的详细信息
            if processed_count <= 10:
                print(f"{stock_code}: P1当前={p1_current}, P1前={p1_prev}, P2当前={p2_current}, P2前={p2_prev}, 条件1={condition1}, 条件2={condition2}")
            
            if condition1 and condition2:
                selected_stocks.append(stock_code)
                print(f"✓ 选中: {stock_code}")
        
        except Exception as e:
            print(f"处理{stock_code}时出错: {e}")
            continue
    
    print(f"\n统计结果:")
    print(f"处理股票数: {processed_count}")
    print(f"满足条件1的股票数: {condition1_count}")
    print(f"满足条件2的股票数: {condition2_count}")
    print(f"同时满足两个条件的股票数: {len(selected_stocks)}")
    print(f"选股成功率: {len(selected_stocks)/processed_count*100:.2f}%")
    
    return selected_stocks


def create_specific_test_cases():
    """创建特定的测试用例，确保能选出股票"""
    print("\n" + "="*60)
    print("创建特定测试用例")
    print("="*60)
    
    # 基于之前成功的数据模式创建测试用例
    test_cases = [
        {
            "code": "PERFECT.SH",
            "closes": [12.0, 11.0, 10.0, 8.0, 11.0, 12.0],  # 前一日8.0很低，当前11.0回升
            "opens":  [11.8, 10.8, 9.8,  8.8, 10.5, 11.5]   # 前一日开盘8.8，当前开盘10.5
        },
        {
            "code": "GOOD.SZ", 
            "closes": [15.0, 14.0, 13.0, 11.5, 14.0, 15.5],
            "opens":  [14.8, 13.8, 12.8, 12.0, 13.5, 15.0]
        }
    ]
    
    selected_stocks = []
    
    for case in test_cases:
        stock_code = case["code"]
        closes = np.array(case["closes"])
        opens = np.array(case["opens"])
        
        print(f"\n测试股票: {stock_code}")
        print(f"收盘价: {closes}")
        print(f"开盘价: {opens}")
        
        # 计算P1P2
        p1_current, p1_prev, p2_current, p2_prev = calculate_p1p2(closes, opens)
        
        if p1_current is None:
            print("数据不足")
            continue
        
        print(f"P1当前={p1_current}, P1前={p1_prev}, P2当前={p2_current}, P2前={p2_prev}")
        
        # 选股条件
        condition1 = p1_current == 0 and p1_prev == 1
        condition2 = p2_current == 0 and p2_prev == 1
        
        print(f"条件1: {condition1}, 条件2: {condition2}")
        
        if condition1 and condition2:
            selected_stocks.append(stock_code)
            print(f"✓ 选中股票: {stock_code}")
        else:
            print(f"✗ 未选中: {stock_code}")
    
    return selected_stocks


def analyze_selection_difficulty():
    """分析P1P2选股条件的难度"""
    print("\n" + "="*60)
    print("分析P1P2选股条件的难度")
    print("="*60)
    
    print("P1P2选股条件分析:")
    print("条件1: P1=0 AND REF(P1,1)=1")
    print("条件2: P2=0 AND REF(P2,1)=1")
    print()
    print("这意味着需要找到同时满足以下条件的股票:")
    print("1. 前一日弱势：收盘价 < 前1日开盘价 AND 收盘价 < 当日开盘价")
    print("2. 前一日弱势：收盘价 < 前2日开盘价 AND 收盘价 < 当日开盘价") 
    print("3. 当前日强势：NOT(收盘价 < 前1日开盘价 AND 收盘价 < 当日开盘价)")
    print("4. 当前日强势：NOT(收盘价 < 前2日开盘价 AND 收盘价 < 当日开盘价)")
    print()
    print("这是一个相当严格的反转信号，在实际市场中出现频率较低。")
    print("建议:")
    print("1. 确认通达信选股结果是否真的有股票")
    print("2. 检查数据获取是否正确")
    print("3. 考虑放宽部分条件或调整参数")


def main():
    """主函数"""
    print("P1P2选股测试开始")
    print("当前时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 分析选股难度
    analyze_selection_difficulty()
    
    # 使用随机数据测试
    random_selected = test_p1p2_selection_with_random_data()
    
    # 使用特定测试用例
    specific_selected = create_specific_test_cases()
    
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    print(f"随机数据测试选中股票数: {len(random_selected)}")
    print(f"特定用例测试选中股票数: {len(specific_selected)}")
    
    if len(random_selected) > 0:
        print("✓ P1P2选股逻辑正常，能够找到符合条件的股票")
    else:
        print("⚠ P1P2选股条件可能过于严格，建议检查实际数据")
    
    print("\n建议:")
    print("1. 在实际环境中运行时，确保能获取到足够的历史数据")
    print("2. 检查股票代码格式是否正确")
    print("3. 验证通达信选股结果，对比差异")
    print("4. 考虑增加调试日志，跟踪选股过程")


if __name__ == "__main__":
    main()
