# 股票交易策略修复说明文档

## 修复概述

本次修复针对三个股票交易策略中存在的问题进行了全面优化，主要解决了选股公式错误、实时监控缺失、股票池识别问题等关键问题。

## 策略1：P1P2技术指标选股 - 修复内容

### 问题分析
1. **P1P2公式计算错误**：原代码中P1、P2指标计算逻辑不符合通达信公式
2. **选股时间限制**：只在9:30-9:35选股一次，无法实时监控
3. **缺少新股过滤**：未排除上市不足60天的新股

### 修复内容
1. **修正P1P2公式计算**：
   ```python
   # 修正后的P1计算：P1:=IF(C<REF(O,1) AND C<O,1,0);
   p1_today = 1 if (current_close < prev1_open and current_close < current_open) else 0
   p1_yesterday = 1 if (prev1_close < prev2_open and prev1_close < prev1_open) else 0
   
   # 修正后的P2计算：P2:=IF(C<REF(O,2) AND C<O,1,0);
   p2_today = 1 if (current_close < prev2_open and current_close < current_open) else 0
   p2_yesterday = 1 if (prev1_close < prev3_open and prev1_close < prev1_open) else 0
   
   # 选股条件: P1=0 AND REF(P1,1)=1 AND P2=0 AND REF(P2,1)=1
   condition1 = p1_today == 0 and p1_yesterday == 1
   condition2 = p2_today == 0 and p2_yesterday == 1
   ```

2. **实现实时循环选股**：
   - 改为每5分钟重新选股一次
   - 在整个交易时间内持续监控
   - 动态更新选股列表

3. **添加新股过滤**：
   ```python
   def is_new_stock(self, stock_code):
       """判断是否为新股（上市不足60天）"""
       # 检查上市时间，排除上市不足60天的股票
   ```

4. **优化资金管理**：
   - 第一只股票使用50%资金
   - 第二只股票使用剩余95%资金（留5%手续费缓冲）

## 策略2：股票池选股 - 修复内容

### 问题分析
1. **股票池文件读取问题**：无法正确识别7位数字代码
2. **缺少QMT后缀**：股票代码缺少.SH/.SZ后缀
3. **涨停监控逻辑错误**：未正确处理涨停开盘后打开的情况

### 修复内容
1. **改进股票池读取逻辑**：
   ```python
   # 处理7位数字代码，截取为6位
   if code.isdigit() and len(code) == 7:
       code = code[1:]  # 去掉第一位
   
   # 为6位数字代码添加后缀
   if code.isdigit() and len(code) == 6:
       if code.startswith('6'):
           code = code + '.SH'  # 上海股票
       elif code.startswith(('0', '3')):
           code = code + '.SZ'  # 深圳股票
   ```

2. **优化选股条件**：
   - 排除开盘涨幅小于7%的股票
   - 保留涨幅大于7%且不涨停的股票
   - 监控涨停开盘股票，等待打开后买入

3. **实现实时循环监控**：
   - 每3分钟重新选股一次
   - 持续监控买入条件

## 策略3：老鼠仓选股 - 修复内容

### 问题分析
1. **买入条件错误**：原代码在涨停价买入（无法成交）
2. **选股频率问题**：只选股一次，无法发现新的老鼠仓形态

### 修复内容
1. **修正买入条件**：
   ```python
   # 从涨停价买入改为9.96%涨幅买入
   if price_change_rate >= 9.96:
       if not self.is_limit_up(stock_code, current_price, prev_close):
           return True
   ```

2. **实现实时循环选股**：
   - 每10分钟重新选股一次（老鼠仓形态相对稳定）
   - 在交易时间内持续监控

## 通用优化

### 1. 统一时间管理
- 所有策略统一交易时间：9:30-14:55
- 实现真正的实时选股和监控
- 移除一次性选股限制

### 2. 改进资金管理
- 动态半仓买入逻辑
- 第一只股票50%资金，第二只股票剩余资金
- 预留手续费缓冲

### 3. 增强错误处理
- 完善异常捕获和日志记录
- 提供详细的调试信息
- 优化程序稳定性

### 4. 优化监控逻辑
- 买入2只股票后自动停止监控
- 避免重复买入同一股票
- 实时更新选股列表

## 使用说明

### 1. 环境要求
- Python 3.7+
- QMT客户端及Python API
- 必要的Python包：pandas, numpy等

### 2. 配置参数
```python
ACCOUNT_ID = "**********"  # 修改为您的资金账号
STOCK_POOL_PATH = r"D:\tdx\T0002\blocknew\1J2.blk"  # 修改为实际股票池路径
```

### 3. 运行方式
```bash
# 运行单个策略
python 策略1_P1P2技术指标选股.py

# 运行主控制程序
python 主控制程序.py

# 运行测试验证
python 测试验证脚本.py
```

### 4. 注意事项
1. **测试环境**：建议先在模拟环境中测试
2. **资金安全**：确认账户ID和资金额度
3. **股票池路径**：确保股票池文件存在且格式正确
4. **QMT连接**：确保QMT客户端已启动并登录

## 测试验证

运行 `测试验证脚本.py` 可以验证：
- 模块导入是否正常
- 基础功能是否工作
- 时间判断逻辑是否正确
- 依赖项是否完整

## 风险提示

1. **市场风险**：股票投资存在市场风险，请谨慎操作
2. **技术风险**：程序可能存在未知bug，建议充分测试
3. **资金风险**：请合理控制仓位，避免过度投资
4. **合规风险**：请确保交易行为符合相关法规

## 后续优化建议

1. **添加止损功能**：实现自动止损保护
2. **优化选股算法**：提高选股准确率
3. **增加回测功能**：验证策略有效性
4. **完善监控界面**：提供可视化监控
5. **添加风控模块**：增强风险控制能力
