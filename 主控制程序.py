# -*- coding: utf-8 -*-
"""
QMT自动交易策略主控制程序
选择运行单个策略或多个策略组合
"""

import sys
import time
import threading
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def show_menu():
    """显示菜单"""
    print("\n" + "=" * 60)
    print("           QMT自动交易策略控制台")
    print("=" * 60)
    print("策略选择:")
    print("1. 策略1 - P1P2技术指标选股 (涨幅达9.96%买入)")
    print("2. 策略2 - 股票池选股 (涨幅>7%且不涨停, 达9.96%买入)")
    print("3. 策略3 - 老鼠仓选股 (达到涨停价买入)")
    print("4. 运行所有策略 (并行运行)")
    print("5. 策略状态监控")
    print("0. 退出程序")
    print("=" * 60)


def run_strategy1():
    """运行策略1"""
    try:
        print("正在启动策略1 - P1P2技术指标选股...")

        # 这里需要导入策略1模块
        # 由于文件分离，您需要将策略1的代码保存为单独的.py文件
        from 策略1_P1P2技术指标选股 import Strategy1_P1P2_Trading

        ACCOUNT_ID = "**********"
        strategy1 = Strategy1_P1P2_Trading(ACCOUNT_ID)
        strategy1.run_strategy()

    except ImportError:
        print("请先将策略1代码保存为 '策略1_P1P2技术指标选股.py' 文件")
        print("或者直接运行该文件")
    except Exception as e:
        logger.error(f"策略1运行失败: {e}")


def run_strategy2():
    """运行策略2"""
    try:
        print("正在启动策略2 - 股票池选股...")

        from 策略2_股票池选股 import Strategy2_StockPool_Trading

        ACCOUNT_ID = "**********"
        STOCK_POOL_PATH = r"D:\tdx\T0002\blocknew\1J2.blk"
        strategy2 = Strategy2_StockPool_Trading(ACCOUNT_ID, STOCK_POOL_PATH)
        strategy2.run_strategy()

    except ImportError:
        print("请先将策略2代码保存为 '策略2_股票池选股.py' 文件")
        print("或者直接运行该文件")
    except Exception as e:
        logger.error(f"策略2运行失败: {e}")


def run_strategy3():
    """运行策略3"""
    try:
        print("正在启动策略3 - 老鼠仓选股...")

        from 策略3_老鼠仓选股 import Strategy3_LaoshuCang_Trading

        ACCOUNT_ID = "**********"
        strategy3 = Strategy3_LaoshuCang_Trading(ACCOUNT_ID)
        strategy3.run_strategy()

    except ImportError:
        print("请先将策略3代码保存为 '策略3_老鼠仓选股.py' 文件")
        print("或者直接运行该文件")
    except Exception as e:
        logger.error(f"策略3运行失败: {e}")


def run_all_strategies():
    """并行运行所有策略"""
    print("正在启动所有策略...")

    try:
        # 创建线程运行各个策略
        thread1 = threading.Thread(target=run_strategy1, name="Strategy1")
        thread2 = threading.Thread(target=run_strategy2, name="Strategy2")
        thread3 = threading.Thread(target=run_strategy3, name="Strategy3")

        # 设置为守护线程
        thread1.daemon = True
        thread2.daemon = True
        thread3.daemon = True

        # 启动所有策略
        thread1.start()
        time.sleep(2)  # 错开启动时间
        thread2.start()
        time.sleep(2)
        thread3.start()

        print("所有策略已启动，按Ctrl+C退出...")

        # 保持主线程运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n接收到退出信号，正在停止所有策略...")

    except Exception as e:
        logger.error(f"运行所有策略失败: {e}")


def show_strategy_status():
    """显示策略状态"""
    print("\n策略状态监控:")
    print("-" * 40)
    print("当前时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

    # 检查是否在交易时间
    now = datetime.now()
    if now.weekday() >= 5:
        print("状态: 非交易日 (周末)")
    else:
        trading_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
        trading_end = now.replace(hour=15, minute=0, second=0, microsecond=0)
        selection_start = now.replace(hour=9, minute=25, second=0, microsecond=0)
        selection_end = now.replace(hour=9, minute=30, second=0, microsecond=0)

        if selection_start <= now <= selection_end:
            print("状态: 选股时间 (9:25-9:30)")
        elif trading_start <= now <= trading_end:
            print("状态: 交易时间 (9:30-15:00)")
        else:
            print("状态: 非交易时间")

    print("-" * 40)
    print("策略说明:")
    print("策略1: 基于P1、P2技术指标选股")
    print("策略2: 从通达信股票池筛选强势股")
    print("策略3: 基于老鼠仓形态选股")
    print("所有策略均采用半仓买入，不考虑卖出")


def main():
    """主函数"""
    print("QMT自动交易策略启动中...")

    # 检查基本环境
    try:
        from xtquant import xtdata
        print("✓ xtquant模块检查通过")
    except ImportError:
        print("✗ xtquant模块未安装，请先安装QMT的Python API")
        input("按回车键退出...")
        return

    while True:
        try:
            show_menu()
            choice = input("请选择操作 (0-5): ").strip()

            if choice == '0':
                print("退出程序...")
                break
            elif choice == '1':
                run_strategy1()
            elif choice == '2':
                run_strategy2()
            elif choice == '3':
                run_strategy3()
            elif choice == '4':
                run_all_strategies()
            elif choice == '5':
                show_strategy_status()
                input("\n按回车键返回主菜单...")
            else:
                print("无效选择，请重新输入")

        except KeyboardInterrupt:
            print("\n程序被用户中断")
            break
        except Exception as e:
            logger.error(f"程序异常: {e}")
            print(f"发生错误: {e}")


if __name__ == "__main__":
    main()