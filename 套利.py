# -*- coding: UTF-8 -*-
"""
@Project ：stock_code
@File    ：策略2_股票池选股.py
@IDE     ：PyCharm
<AUTHOR>
@Date    ：2025/7/9 15:41:45
"""
# -*- coding: utf-8 -*-
"""
策略2：股票池选股自动交易
从通达信股票池筛选开盘涨幅大于7%的股票（包括涨停股），
对于非涨停股，涨幅达到9.96%时自动买入半仓；
对于涨停开盘的股票，打开涨停后涨幅重新达到9.96%时买入半仓。
选股时间改为9:30后。
"""

import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
import os
from xtquant import xtdata
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount
from xtquant import xtconstant
import threading
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("strategy2.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class Strategy2Callback(XtQuantTraderCallback):
    """策略2回调类"""

    def on_disconnected(self):
        logger.warning("策略2 - QMT连接断开")

    def on_stock_order(self, order):
        logger.info(f"策略2 - 委托回调: {order.stock_code}, 状态: {order.order_status}")

    def on_stock_trade(self, trade):
        logger.info(f"策略2 - 成交回调: {trade.stock_code}, 数量: {trade.traded_volume}")

    def on_order_error(self, order_error):
        logger.error(f"策略2 - 委托失败: {order_error.error_msg}")


class Strategy2_StockPool_Trading:
    def __init__(self, account_id, stock_pool_path=None, mini_qmt_path=None):
        """
        初始化策略2：股票池选股策略
        """
        self.account_id = account_id
        self.session_id = int(time.time()) + 1  # 避免与策略1冲突

        # 股票池路径
        if stock_pool_path is None:
            self.stock_pool_path = r"D:\tdx\T0002\blocknew\1J2.blk"
        else:
            self.stock_pool_path = stock_pool_path

        # 设置MiniQMT路径
        if mini_qmt_path is None:
            possible_paths = [
                r'D:\国金证券QMT交易端\userdata_mini',
                r'D:\国金证券QMT交易端\bin.x64\userdata_mini',
                r'D:\国金证券QMT交易端\bin\userdata_mini'
            ]

            mini_qmt_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    mini_qmt_path = path
                    logger.info(f"策略2 - 找到userdata_mini路径: {path}")
                    break

            if mini_qmt_path is None:
                raise Exception("策略2 - 未找到userdata_mini目录")

        try:
            # 初始化QMT连接
            self.trader = XtQuantTrader(mini_qmt_path, self.session_id)
            self.account = StockAccount(account_id)

            # 注册回调
            self.callback = Strategy2Callback()
            self.trader.register_callback(self.callback)

            # 启动并连接
            logger.info("策略2 - 启动QMT交易客户端...")
            self.trader.start()

            logger.info("策略2 - 建立QMT连接...")
            connect_result = self.trader.connect()
            if connect_result == 0:
                logger.info("策略2 - QMT连接成功")
            else:
                raise Exception(f"策略2 - QMT连接失败，错误码: {connect_result}")

            # 订阅账户
            self.trader.subscribe(self.account)

        except Exception as e:
            logger.error(f"策略2 - 初始化失败: {e}")
            raise

        # 策略状态
        self.is_running = False
        self.selected_stocks = []  # 选中的股票（开盘涨幅≥7%的股票）
        self.monitoring_stocks = []  # 交易时段监控的股票（非涨停股）
        self.bought_stocks = set()  # 已买入的股票
        self.limit_up_stocks = set()  # 一字板开盘的股票
        self.opened_limit_stocks = set()  # 曾经涨停但已打开的股票
        self.last_minute_data = {}  # 缓存分钟级数据
        self.xtdata_ready = False  # xtdata连接状态标志
        self.data_retry_count = 5  # 数据获取重试次数
        self.data_retry_delay = 0.8  # 数据获取重试延迟(秒)

        # 市场后缀映射
        self.market_suffix_map = {
            '60': '.SH',  # 沪市
            '00': '.SZ',  # 深市主板
            '30': '.SZ',  # 创业板
            '68': '.SH',  # 科创板
            '43': '.BJ',  # 北交所
            '83': '.BJ',  # 北交所
            '87': '.BJ',  # 北交所
            '002': '.SZ',  # 中小板
            '300': '.SZ',  # 创业板
            '688': '.SH'  # 科创板
        }

        # 在初始化时立即读取股票池
        self.stock_pool = self.read_stock_pool(self.stock_pool_path)
        logger.info(f"策略2 - 初始化时读取股票池，共{len(self.stock_pool)}只股票")

    def wait_for_xtdata_connection(self, timeout=60):
        """等待xtdata连接就绪"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                # 测试连接
                test_data = xtdata.get_market_data(['000001.SZ'], period='1m', count=1)
                if test_data is not None:
                    self.xtdata_ready = True
                    logger.info("策略2 - xtdata连接就绪")
                    return True
            except Exception as e:
                logger.warning(f"策略2 - 等待xtdata连接中... ({e})")
            time.sleep(2)
        logger.error("策略2 - 等待xtdata连接超时")
        return False

    def is_trading_time(self):
        """检查是否在交易时间内（9:30-14:55）"""
        now = datetime.now()
        if now.weekday() >= 5:  # 跳过周末
            return False
        trading_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
        trading_end = now.replace(hour=14, minute=55, second=0, microsecond=0)
        return trading_start <= now <= trading_end

    def validate_stock_code(self, code):
        """验证股票代码格式"""
        parts = code.split('.')
        if len(parts) != 2:
            return False
        if parts[1] not in ['SH', 'SZ', 'BJ']:
            return False
        return len(parts[0]) == 6 and parts[0].isdigit()

    def read_stock_pool(self, file_path):
        """读取通达信股票池文件，并转换为标准格式"""
        try:
            if not os.path.exists(file_path):
                logger.warning(f"策略2 - 股票池文件不存在: {file_path}")
                # 返回示例股票池
                return ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']

            with open(file_path, 'r', encoding='gbk') as f:
                lines = f.readlines()

            stocks = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    # 解析股票代码
                    if '|' in line:
                        code = line.split('|')[0].strip()
                    else:
                        code = line

                    # 转换格式：添加市场后缀
                    if len(code) in (6, 7) and code.isdigit():  # 支持6位和7位数字代码
                        # 处理7位代码：截取后6位
                        if len(code) == 7:
                            original_code = code
                            code = code[-6:]  # 截取后6位
                            logger.info(f"策略2 - 转换7位代码 {original_code} 为6位: {code}")

                        # 处理特殊板块
                        prefix = code[:2]
                        if code.startswith('002'):
                            suffix = '.SZ'  # 中小板
                        elif code.startswith('300'):
                            suffix = '.SZ'  # 创业板
                        elif code.startswith('688'):
                            suffix = '.SH'  # 科创板
                        else:
                            suffix = self.market_suffix_map.get(prefix, '.SZ')  # 默认深市
                        stocks.append(code + suffix)
                    elif '.' in code:
                        # 已经是标准格式
                        stocks.append(code)
                    else:
                        logger.warning(f"策略2 - 忽略无法识别的股票代码格式: {code}")

            # 验证股票代码有效性
            valid_stocks = []
            for stock in stocks:
                if self.validate_stock_code(stock):
                    valid_stocks.append(stock)
                else:
                    logger.warning(f"策略2 - 忽略无效股票代码格式: {stock}")

            logger.info(f"策略2 - 从股票池读取到{len(valid_stocks)}只有效股票")
            return valid_stocks

        except Exception as e:
            logger.error(f"策略2 - 读取股票池文件失败: {e}")
            # 返回示例股票池
            return ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']

    def is_main_board_stock(self, stock_code):
        """判断是否为主板股票"""
        # 提取纯数字代码
        code = stock_code.split('.')[0]
        return code.startswith('60') or code.startswith('00')

    def is_limit_up(self, stock_code, current_price, prev_close):
        """判断是否涨停"""
        if self.is_main_board_stock(stock_code):
            limit_up_price = round(prev_close * 1.1, 2)  # 主板10%
        else:
            limit_up_price = round(prev_close * 1.2, 2)  # 创业板20%
        return abs(current_price - limit_up_price) < 0.015  # 考虑浮点误差

    def calculate_price_change_rate(self, current_price, prev_close):
        """计算涨幅"""
        if prev_close == 0:
            return 0
        return (current_price - prev_close) / prev_close * 100

    def get_stock_data(self, stock_code, period='1m', count=1):
        """多途径获取股票数据"""
        # 尝试方式1：xtdata
        for attempt in range(self.data_retry_count):
            try:
                data = xtdata.get_market_data([stock_code], period=period, count=count)
                if data is not None and len(data) > 0:
                    return data
                else:
                    logger.debug(f"策略2 - 第{attempt + 1}次尝试: {stock_code}的{period}数据为空")
            except Exception as e:
                logger.debug(f"策略2 - 第{attempt + 1}次尝试: xtdata获取{stock_code}数据失败: {e}")

            # 等待后重试
            time.sleep(self.data_retry_delay * (attempt + 1))

        logger.warning(f"策略2 - 无法获取{stock_code}的{period}数据")
        return None

    def get_stock_price(self, stock_code):
        """获取股票当前价格和昨收价，使用多种数据源"""
        # 先尝试分钟线数据
        min_data = self.get_stock_data(stock_code, period='1m', count=1)
        if min_data is not None and len(min_data) > 0:
            current_price = min_data['close'][0]
            # 尝试从分钟线数据获取昨收价
            if 'preClose' in min_data:
                prev_close = min_data['preClose'][0]
            else:
                # 如果分钟线没有昨收价，尝试日线数据
                daily_data = self.get_stock_data(stock_code, period='1d', count=1)
                if daily_data is not None and len(daily_data) > 0:
                    prev_close = daily_data['close'][0]  # 日线数据的close是昨收价
                else:
                    prev_close = None
            return current_price, prev_close

        # 分钟线数据失败，尝试tick数据
        tick_data = self.get_stock_data(stock_code, period='tick', count=1)
        if tick_data is not None and len(tick_data) > 0:
            current_price = tick_data['lastPrice'][0]
            prev_close = tick_data['preClose'][0] if 'preClose' in tick_data else None
            return current_price, prev_close

        # 最后尝试日线数据
        daily_data = self.get_stock_data(stock_code, period='1d', count=1)
        if daily_data is not None and len(daily_data) > 0:
            # 使用日线数据的最新收盘价作为当前价（不太准确，但作为备选）
            current_price = daily_data['close'][0]
            # 昨收价就是前一日的收盘价
            prev_close = daily_data['close'][0] if len(daily_data['close']) > 1 else None
            return current_price, prev_close

        logger.error(f"策略2 - 完全无法获取{stock_code}的价格数据")
        return None, None

    def stock_pool_selection(self):
        """股票池选股逻辑 - 只保留开盘涨幅≥7%的股票（包括涨停股）"""
        try:
            # 使用初始化时读取的股票池
            stock_pool = self.stock_pool
            selected_stocks = []
            self.limit_up_stocks = set()  # 重置涨停股票集合

            logger.info(f"策略2 - 开始股票池选股，股票池包含{len(stock_pool)}只股票")

            for stock_code in stock_pool:
                try:
                    # 获取股票价格数据
                    current_price, prev_close = self.get_stock_price(stock_code)

                    if current_price is None or prev_close is None:
                        logger.warning(f"策略2 - 无法获取{stock_code}的价格数据，跳过")
                        continue

                    # 计算涨幅
                    price_change_rate = self.calculate_price_change_rate(current_price, prev_close)

                    # 筛选条件：开盘涨幅≥7%的股票（包括涨停股）
                    if price_change_rate >= 7:
                        selected_stocks.append(stock_code)
                        logger.info(f"策略2 - 选中股票: {stock_code}, 开盘涨幅: {price_change_rate:.2f}%")

                        # 记录一字板开盘的股票
                        if self.is_limit_up(stock_code, current_price, prev_close):
                            self.limit_up_stocks.add(stock_code)
                            logger.info(f"策略2 - 发现涨停股票{stock_code}，加入涨停监控列表")

                except Exception as e:
                    logger.error(f"策略2 - 处理股票{stock_code}时出错: {e}")
                    continue

            return selected_stocks

        except Exception as e:
            logger.error(f"策略2 - 选股失败: {e}")
            return []

    def get_realtime_price(self, stock_code):
        """获取实时价格，优先使用tick数据，失败时使用分钟线数据"""
        # 尝试获取tick数据
        tick_data = self.get_stock_data(stock_code, period='tick', count=1)
        if tick_data is not None and len(tick_data) > 0:
            current_price = tick_data['lastPrice'][0]
            prev_close = tick_data['preClose'][0] if 'preClose' in tick_data else None
            return current_price, prev_close, True

        # 尝试获取分钟线数据
        min_data = self.get_stock_data(stock_code, period='1m', count=1)
        if min_data is not None and len(min_data) > 0:
            current_price = min_data['close'][0]
            prev_close = min_data['preClose'][0] if 'preClose' in min_data else None
            return current_price, prev_close, False

        # 尝试使用缓存数据
        if stock_code in self.last_minute_data:
            return self.last_minute_data[stock_code]

        logger.warning(f"策略2 - 无法获取{stock_code}行情数据")
        return None, None, False

    def update_monitoring_stocks(self):
        """更新监控股票列表（非涨停股）"""
        # 重置监控列表
        new_monitoring_stocks = []
        changes_detected = False

        for stock_code in self.selected_stocks:
            try:
                # 跳过已买入的股票
                if stock_code in self.bought_stocks:
                    continue

                # 跳过涨停股（除非是曾经涨停但已打开的股票）
                if stock_code in self.limit_up_stocks and stock_code not in self.opened_limit_stocks:
                    continue

                # 获取实时价格
                current_price, prev_close, is_tick = self.get_realtime_price(stock_code)
                if current_price is None or prev_close is None:
                    continue

                # 缓存数据供后续使用
                self.last_minute_data[stock_code] = (current_price, prev_close, is_tick)

                # 计算涨幅
                price_change_rate = self.calculate_price_change_rate(current_price, prev_close)

                # 检查是否满足监控条件：涨幅>7%且不涨停（或者曾经涨停但已打开）
                should_monitor = True  # 所有开盘涨幅≥7%的股票都应该监控

                # 检查状态变化
                currently_monitored = stock_code in self.monitoring_stocks

                if should_monitor and not currently_monitored:
                    # 新加入监控
                    new_monitoring_stocks.append(stock_code)
                    logger.info(f"策略2 - 加入监控: {stock_code}, 涨幅: {price_change_rate:.2f}%")
                    changes_detected = True
                elif should_monitor and currently_monitored:
                    # 继续保持监控
                    new_monitoring_stocks.append(stock_code)
                elif not should_monitor and currently_monitored:
                    # 从监控中移除
                    logger.info(f"策略2 - 移除监控: {stock_code}, 涨幅: {price_change_rate:.2f}%")
                    changes_detected = True

            except Exception as e:
                logger.error(f"策略2 - 更新监控股票失败 {stock_code}: {e}")

        # 更新监控列表
        self.monitoring_stocks = new_monitoring_stocks
        return changes_detected

    def check_buy_condition(self, stock_code):
        """检查买入条件：涨幅达到9.96%"""
        try:
            # 使用缓存数据或获取新数据
            if stock_code in self.last_minute_data:
                current_price, prev_close, _ = self.last_minute_data[stock_code]
            else:
                current_price, prev_close, _ = self.get_realtime_price(stock_code)
                if current_price is None or prev_close is None:
                    return False

            # 计算涨幅
            price_change_rate = self.calculate_price_change_rate(current_price, prev_close)

            # 检查是否达到9.96%买入条件
            if price_change_rate >= 9.96:
                # 检查是否涨停（避免买入涨停股）
                if not self.is_limit_up(stock_code, current_price, prev_close):
                    return True

            return False

        except Exception as e:
            logger.error(f"策略2 - 检查买入条件失败 {stock_code}: {e}")
            return False

    def place_buy_order(self, stock_code):
        """下买单（半仓）"""
        try:
            # 获取账户资金
            account_info = self.trader.query_stock_asset(self.account)
            if account_info is None:
                logger.error("策略2 - 无法获取账户信息")
                return False

            available_cash = account_info.cash
            half_cash = available_cash * 0.5  # 半仓

            # 获取当前价格
            current_price, _, _ = self.get_realtime_price(stock_code)
            if current_price is None:
                logger.error(f"策略2 - 无法获取{stock_code}当前价格")
                return False

            # 计算可买数量（按100股取整）
            buy_quantity = int(half_cash / current_price / 100) * 100

            if buy_quantity < 100:
                logger.warning(f"策略2 - 资金不足，无法买入{stock_code}，需要至少{100 * current_price:.2f}元")
                return False

            # 下买单
            order_id = self.trader.order_stock(
                account=self.account,
                stock_code=stock_code,
                order_type=xtconstant.STOCK_BUY,
                order_volume=buy_quantity,
                price_type=xtconstant.FIX_PRICE,
                price=current_price,
                strategy_name='strategy2_pool',
                order_remark=f'Pool_{stock_code}'
            )

            if order_id > 0:
                logger.info(f"策略2 - 成功下买单: {stock_code}, 数量: {buy_quantity}, 价格: {current_price}")
                self.bought_stocks.add(stock_code)
                return True
            else:
                logger.error(f"策略2 - 下买单失败: {stock_code}")
                return False

        except Exception as e:
            logger.error(f"策略2 - 下买单异常 {stock_code}: {e}")
            return False

    def start_trading(self):
        """启动交易策略"""
        self.is_running = True
        logger.info("策略2 - 股票池交易策略已启动")

        # 启动主交易线程
        trading_thread = threading.Thread(target=self.main_trading_loop)
        trading_thread.daemon = True
        trading_thread.start()

    def main_trading_loop(self):
        """主交易循环 - 每秒检查一次"""
        # 等待xtdata连接就绪
        if not self.wait_for_xtdata_connection():
            logger.error("策略2 - xtdata连接失败，退出策略")
            self.is_running = False
            return

        last_selection_date = None
        last_log_time = datetime.min
        last_limit_check_time = datetime.min

        # 在循环开始前记录股票池信息
        logger.info(f"策略2 - 已加载股票池: {len(self.stock_pool)}只股票")

        while self.is_running:
            try:
                current_time = datetime.now()
                today = current_time.date()
                today_9_30 = current_time.replace(hour=9, minute=30, second=0, microsecond=0)
                today_9_30_10 = current_time.replace(hour=9, minute=30, second=10, microsecond=0)

                # 选股条件：9:30后且今天还未选股
                if self.is_trading_time() and last_selection_date != today and current_time >= today_9_30_10:
                    logger.info("策略2 - 开始股票池选股...")
                    self.selected_stocks = self.stock_pool_selection()
                    last_selection_date = today

                    if len(self.selected_stocks) > 0:
                        logger.info(f"策略2 - 选股完成，共选中{len(self.selected_stocks)}只开盘涨幅≥7%的股票")
                        logger.info(f"策略2 - 其中涨停股票: {len(self.limit_up_stocks)}只")
                    else:
                        logger.info("策略2 - 选股完成，没有符合条件的股票")

                    # 初始化监控列表
                    self.update_monitoring_stocks()
                    logger.info(f"策略2 - 初始监控股票: {len(self.monitoring_stocks)}只（非涨停股）")

                    # 如果监控列表为空，记录详细信息
                    if len(self.monitoring_stocks) == 0 and len(self.selected_stocks) > 0:
                        logger.info("策略2 - 监控列表为空，但选中了涨停股，等待打开涨停后监控")

                # 交易时间：9:30-14:55
                if self.is_trading_time():
                    # 每秒更新一次监控列表
                    changes_detected = self.update_monitoring_stocks()

                    # 每30秒记录一次状态
                    if (current_time - last_log_time).seconds >= 30:
                        logger.info(f"策略2 - 当前监控股票: {len(self.monitoring_stocks)}只")
                        logger.info(f"策略2 - 涨停股票: {len(self.limit_up_stocks)}只")
                        logger.info(f"策略2 - 已打开涨停的股票: {len(self.opened_limit_stocks)}只")
                        logger.info(f"策略2 - 已买入股票: {len(self.bought_stocks)}只")
                        last_log_time = current_time

                    # 每5秒检查一次一字板股票是否打开涨停
                    if (current_time - last_limit_check_time).seconds >= 5:
                        for stock_code in list(self.limit_up_stocks):
                            try:
                                current_price, prev_close, _ = self.get_realtime_price(stock_code)
                                if current_price is None or prev_close is None:
                                    continue

                                # 如果打开涨停，从涨停集合中移除，并加入已打开涨停集合
                                if not self.is_limit_up(stock_code, current_price, prev_close):
                                    self.limit_up_stocks.remove(stock_code)
                                    self.opened_limit_stocks.add(stock_code)
                                    logger.info(f"策略2 - 涨停股票{stock_code}已打开涨停，加入监控列表")

                                    # 立即更新监控列表
                                    self.update_monitoring_stocks()
                            except Exception as e:
                                logger.error(f"策略2 - 检查涨停股票{stock_code}失败: {e}")
                        last_limit_check_time = current_time

                    # 检查买入条件
                    for stock_code in self.monitoring_stocks:
                        if stock_code in self.bought_stocks:
                            continue

                        if self.check_buy_condition(stock_code):
                            logger.info(f"策略2 - 股票{stock_code}满足买入条件")
                            if self.place_buy_order(stock_code):
                                # 买入后，该股票会被加入bought_stocks，在下次更新监控列表时会自动排除
                                pass

                # 每秒循环一次
                time.sleep(1)

            except Exception as e:
                logger.error(f"策略2 - 主交易循环异常: {e}")
                time.sleep(1)

    def run_strategy(self):
        """运行策略"""
        logger.info("策略2 - 启动股票池自动交易策略...")
        self.start_trading()

        try:
            while self.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("策略2 - 接收到退出信号")
            self.is_running = False
            self.trader.unsubscribe(self.account)
            self.trader.stop()


if __name__ == "__main__":
    # 配置参数
    ACCOUNT_ID = "**********"  # 您的资金账号
    STOCK_POOL_PATH = r"D:\tdx\T0002\blocknew\1J2.blk"  # 通达信股票池路径

    try:
        # 创建并运行策略2
        strategy2 = Strategy2_StockPool_Trading(ACCOUNT_ID, STOCK_POOL_PATH)
        strategy2.run_strategy()

    except Exception as e:
        logger.error(f"策略2 - 程序运行异常: {e}")
        print(f"策略2错误: {e}")
        print("请检查:")
        print("1. QMT客户端是否已启动并登录")
        print("2. 股票池文件路径是否正确")
        print("3. 通达信股票池文件是否存在")