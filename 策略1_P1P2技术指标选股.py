# -*- coding: UTF-8 -*-
"""
@Project ：stock_code 
@File    ：策略1_P1P2技术指标选股.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/9 15:41:29
"""
# -*- coding: utf-8 -*-
"""
策略1：P1P2技术指标选股自动交易
基于P1、P2指标进行选股，涨幅达到9.96%时自动买入半仓
修正：在9:30开盘后进行选股，而非9:25-9:30
"""

import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
import os
from xtquant import xtdata
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount
from xtquant import xtconstant
import threading
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class Strategy1Callback(XtQuantTraderCallback):
    """策略1回调类"""

    def on_disconnected(self):
        logger.warning("策略1 - QMT连接断开")

    def on_stock_order(self, order):
        logger.info(f"策略1 - 委托回调: {order.stock_code}, 状态: {order.order_status}")

    def on_stock_trade(self, trade):
        logger.info(f"策略1 - 成交回调: {trade.stock_code}, 数量: {trade.traded_volume}")

    def on_order_error(self, order_error):
        logger.error(f"策略1 - 委托失败: {order_error.error_msg}")


class Strategy1_P1P2_Trading:
    def __init__(self, account_id, mini_qmt_path=None):
        """
        初始化策略1：P1P2技术指标选股策略
        """
        self.account_id = account_id
        self.session_id = int(time.time())

        # 设置MiniQMT路径
        if mini_qmt_path is None:
            possible_paths = [
                r'D:\国金证券QMT交易端\userdata_mini',
                r'D:\国金证券QMT交易端\bin.x64\userdata_mini',
                r'D:\国金证券QMT交易端\bin\userdata_mini'
            ]

            mini_qmt_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    mini_qmt_path = path
                    logger.info(f"策略1 - 找到userdata_mini路径: {path}")
                    break

            if mini_qmt_path is None:
                raise Exception("策略1 - 未找到userdata_mini目录")

        try:
            # 初始化QMT连接
            self.trader = XtQuantTrader(mini_qmt_path, self.session_id)
            self.account = StockAccount(account_id)

            # 注册回调
            self.callback = Strategy1Callback()
            self.trader.register_callback(self.callback)

            # 启动并连接
            logger.info("策略1 - 启动QMT交易客户端...")
            self.trader.start()

            logger.info("策略1 - 建立QMT连接...")
            connect_result = self.trader.connect()
            if connect_result == 0:
                logger.info("策略1 - QMT连接成功")
            else:
                raise Exception(f"策略1 - QMT连接失败，错误码: {connect_result}")

            # 订阅账户
            self.trader.subscribe(self.account)

        except Exception as e:
            logger.error(f"策略1 - 初始化失败: {e}")
            raise

        # 策略状态
        self.is_running = False
        self.selected_stocks = []
        self.bought_stocks = set()
        self.selection_completed = False  # 新增：标记是否已完成选股

    def is_trading_time(self):
        """检查是否在交易时间内"""
        now = datetime.now()
        if now.weekday() >= 5:  # 跳过周末
            return False
        trading_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
        trading_end = now.replace(hour=14, minute=55, second=0, microsecond=0)
        return trading_start <= now <= trading_end

    def is_stock_selection_time(self):
        """检查是否在选股时间内（策略1在9:30开盘后选股）"""
        now = datetime.now()
        if now.weekday() >= 5:
            return False
        # 策略1在9:30开盘后立即选股
        selection_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
        selection_end = now.replace(hour=9, minute=35, second=0, microsecond=0)  # 给5分钟时间完成选股
        return selection_start <= now <= selection_end

    def get_stock_data(self, stock_code, period='1d', count=10):
        """获取股票数据"""
        try:
            data = xtdata.get_market_data(
                stock_list=[stock_code],
                period=period,
                count=count,
                fill_data=True
            )
            return data
        except Exception as e:
            logger.error(f"策略1 - 获取股票数据失败 {stock_code}: {e}")
            return None

    def is_main_board_stock(self, stock_code):
        """判断是否为主板股票"""
        return stock_code.startswith('60') or stock_code.startswith('00')

    def is_st_stock(self, stock_code):
        """判断是否为ST股票"""
        try:
            info = xtdata.get_instrument_detail(stock_code)
            name = info.get('name', '')
            return 'ST' in name or '*ST' in name
        except:
            return False

    def is_delisted_stock(self, stock_code):
        """判断是否为退市股票"""
        try:
            info = xtdata.get_instrument_detail(stock_code)
            name = info.get('name', '')
            return '退' in name or name.startswith('*')
        except:
            return False

    def is_limit_up(self, stock_code, current_price, prev_close):
        """判断是否涨停"""
        if self.is_main_board_stock(stock_code):
            limit_up_price = prev_close * 1.1  # 主板10%
        else:
            limit_up_price = prev_close * 1.2  # 创业板20%
        return abs(current_price - limit_up_price) < 0.01

    def calculate_price_change_rate(self, current_price, prev_close):
        """计算涨幅"""
        if prev_close == 0:
            return 0
        return (current_price - prev_close) / prev_close * 100

    def p1p2_stock_selection(self):
        """P1P2选股逻辑（开盘后执行）"""
        try:
            all_stocks = xtdata.get_stock_list_in_sector('A股')
            selected_stocks = []

            logger.info(f"策略1 - 开始P1P2选股（开盘后），共{len(all_stocks)}只股票")

            # 限制处理数量以提高效率
            for stock_code in all_stocks[:100]:
                try:
                    # 基本过滤条件
                    if not self.is_main_board_stock(stock_code):
                        continue
                    if self.is_st_stock(stock_code):
                        continue
                    if self.is_delisted_stock(stock_code):
                        continue

                    # 获取历史数据
                    data = self.get_stock_data(stock_code, period='1d', count=5)
                    if data is None or len(data) < 3:
                        continue

                    # 获取价格数据
                    closes = data['close'].values
                    opens = data['open'].values

                    if len(closes) < 3:
                        continue

                    # 计算P1和P2指标
                    current_close = closes[-1]
                    current_open = opens[-1]
                    prev_open_1 = opens[-2] if len(opens) >= 2 else 0
                    prev_open_2 = opens[-3] if len(opens) >= 3 else 0

                    # P1:=IF(C<REF(O,1) AND C<O,1,0);
                    p1_current = 1 if (current_close < prev_open_1 and current_close < current_open) else 0
                    p1_prev = 1 if (closes[-2] < opens[-3] and closes[-2] < opens[-2]) else 0

                    # P2:=IF(C<REF(O,2) AND C<O,1,0);
                    p2_current = 1 if (current_close < prev_open_2 and current_close < current_open) else 0
                    p2_prev = 1 if (closes[-2] < opens[-3] and closes[-2] < opens[-2]) else 0

                    # 选股条件
                    condition1 = p1_current == 0 and p1_prev == 1
                    condition2 = p2_current == 0 and p2_prev == 1
                    condition3 = current_close / closes[-2] > 1.05

                    # 排除开盘时已经涨停的股票
                    prev_close = closes[-2]
                    current_data = xtdata.get_market_data([stock_code], period='tick', count=1)
                    if current_data is not None:
                        tick_price = current_data['lastPrice'][0]
                        # 在9:30后选股时，排除当前已经涨停的股票
                        if self.is_limit_up(stock_code, tick_price, prev_close):
                            continue

                    if condition1 and condition2 and condition3:
                        selected_stocks.append(stock_code)
                        logger.info(f"策略1 - 选中股票: {stock_code}")

                except Exception as e:
                    logger.error(f"策略1 - 处理股票{stock_code}时出错: {e}")
                    continue

            return selected_stocks

        except Exception as e:
            logger.error(f"策略1 - 选股失败: {e}")
            return []

    def check_buy_condition(self, stock_code):
        """检查买入条件：涨幅达到9.96%"""
        try:
            current_data = xtdata.get_market_data([stock_code], period='tick', count=1)
            if current_data is None:
                return False

            current_price = current_data['lastPrice'][0]
            prev_close = current_data['preClose'][0]
            price_change_rate = self.calculate_price_change_rate(current_price, prev_close)

            return price_change_rate >= 9.96

        except Exception as e:
            logger.error(f"策略1 - 检查买入条件失败 {stock_code}: {e}")
            return False

    def place_buy_order(self, stock_code):
        """下买单（半仓）"""
        try:
            # 获取账户资金
            account_info = self.trader.query_stock_asset(self.account)
            if account_info is None:
                logger.error("策略1 - 无法获取账户信息")
                return False

            available_cash = account_info.cash
            half_cash = available_cash * 0.5  # 半仓

            # 获取当前价格
            current_data = xtdata.get_market_data([stock_code], period='tick', count=1)
            if current_data is None:
                logger.error(f"策略1 - 无法获取{stock_code}当前价格")
                return False

            current_price = current_data['lastPrice'][0]
            buy_quantity = int(half_cash / current_price / 100) * 100

            if buy_quantity < 100:
                logger.warning(f"策略1 - 资金不足，无法买入{stock_code}")
                return False

            # 下买单
            order_id = self.trader.order_stock(
                account=self.account,
                stock_code=stock_code,
                order_type=xtconstant.STOCK_BUY,
                order_volume=buy_quantity,
                price_type=xtconstant.FIX_PRICE,
                price=current_price,
                strategy_name='strategy1_p1p2',
                order_remark=f'P1P2_{stock_code}'
            )

            if order_id > 0:
                logger.info(f"策略1 - 成功下买单: {stock_code}, 数量: {buy_quantity}, 价格: {current_price}")
                self.bought_stocks.add(stock_code)
                return True
            else:
                logger.error(f"策略1 - 下买单失败: {stock_code}")
                return False

        except Exception as e:
            logger.error(f"策略1 - 下买单异常 {stock_code}: {e}")
            return False

    def start_trading(self):
        """启动交易策略"""
        self.is_running = True
        logger.info("策略1 - P1P2交易策略已启动")

        # 启动主交易线程
        trading_thread = threading.Thread(target=self.main_trading_loop)
        trading_thread.daemon = True
        trading_thread.start()

    def main_trading_loop(self):
        """主交易循环"""
        last_selection_date = None

        while self.is_running:
            try:
                current_time = datetime.now()
                today = current_time.date()

                # 每天重置选股状态
                if last_selection_date != today:
                    self.selection_completed = False

                # 选股时间：9:30-9:35（开盘后选股，每天只选一次）
                if (self.is_stock_selection_time() and
                        last_selection_date != today and
                        not self.selection_completed):

                    logger.info("策略1 - 开始P1P2选股（开盘后选股）...")
                    self.selected_stocks = self.p1p2_stock_selection()
                    last_selection_date = today
                    self.selection_completed = True
                    logger.info(f"策略1 - 选股完成，共选中{len(self.selected_stocks)}只股票")

                # 交易时间：9:30-14:55（选股完成后开始监控买入）
                elif self.is_trading_time() and self.selection_completed:
                    for stock_code in self.selected_stocks:
                        if stock_code not in self.bought_stocks:
                            if self.check_buy_condition(stock_code):
                                self.place_buy_order(stock_code)

                time.sleep(1)  # 1秒检查一次

            except Exception as e:
                logger.error(f"策略1 - 主交易循环异常: {e}")
                time.sleep(10)

    def run_strategy(self):
        """运行策略"""
        logger.info("策略1 - 启动P1P2自动交易策略...")
        logger.info("策略1 - 注意：本策略在9:30开盘后进行选股")
        self.start_trading()

        try:
            while self.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("策略1 - 接收到退出信号")
            self.is_running = False
            self.trader.unsubscribe(self.account)
            self.trader.stop()


if __name__ == "__main__":
    # 配置参数
    ACCOUNT_ID = "**********"  # 您的资金账号

    try:
        # 创建并运行策略1
        strategy1 = Strategy1_P1P2_Trading(ACCOUNT_ID)
        strategy1.run_strategy()

    except Exception as e:
        logger.error(f"策略1 - 程序运行异常: {e}")
        print(f"策略1错误: {e}")
        print("请检查QMT客户端是否已启动并登录")