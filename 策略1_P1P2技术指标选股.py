# -*- coding: UTF-8 -*-
"""
@Project ：stock_code 
@File    ：策略1_P1P2技术指标选股.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/9 15:41:29
"""
# -*- coding: utf-8 -*-
"""
策略1：P1P2技术指标选股自动交易
基于P1、P2指标进行选股，涨幅达到9.96%时自动买入半仓
修正：在9:30开盘后进行选股，而非9:25-9:30
"""

import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
import os
from xtquant import xtdata
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount
from xtquant import xtconstant
import threading
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class Strategy1Callback(XtQuantTraderCallback):
    """策略1回调类"""

    def on_disconnected(self):
        logger.warning("策略1 - QMT连接断开")

    def on_stock_order(self, order):
        logger.info(f"策略1 - 委托回调: {order.stock_code}, 状态: {order.order_status}")

    def on_stock_trade(self, trade):
        logger.info(f"策略1 - 成交回调: {trade.stock_code}, 数量: {trade.traded_volume}")

    def on_order_error(self, order_error):
        logger.error(f"策略1 - 委托失败: {order_error.error_msg}")


class Strategy1_P1P2_Trading:
    def __init__(self, account_id, mini_qmt_path=None):
        """
        初始化策略1：P1P2技术指标选股策略
        """
        self.account_id = account_id
        self.session_id = int(time.time())

        # 设置MiniQMT路径
        if mini_qmt_path is None:
            possible_paths = [
                r'D:\国金证券QMT交易端\userdata_mini',
                r'D:\国金证券QMT交易端\bin.x64\userdata_mini',
                r'D:\国金证券QMT交易端\bin\userdata_mini'
            ]

            mini_qmt_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    mini_qmt_path = path
                    logger.info(f"策略1 - 找到userdata_mini路径: {path}")
                    break

            if mini_qmt_path is None:
                raise Exception("策略1 - 未找到userdata_mini目录")

        try:
            # 初始化QMT连接
            self.trader = XtQuantTrader(mini_qmt_path, self.session_id)
            self.account = StockAccount(account_id)

            # 注册回调
            self.callback = Strategy1Callback()
            self.trader.register_callback(self.callback)

            # 启动并连接
            logger.info("策略1 - 启动QMT交易客户端...")
            self.trader.start()

            logger.info("策略1 - 建立QMT连接...")
            connect_result = self.trader.connect()
            if connect_result == 0:
                logger.info("策略1 - QMT连接成功")
            else:
                raise Exception(f"策略1 - QMT连接失败，错误码: {connect_result}")

            # 订阅账户
            self.trader.subscribe(self.account)

        except Exception as e:
            logger.error(f"策略1 - 初始化失败: {e}")
            raise

        # 策略状态
        self.is_running = False
        self.selected_stocks = []
        self.bought_stocks = set()
        self.selection_completed = False  # 新增：标记是否已完成选股

    def is_trading_time(self):
        """检查是否在交易时间内"""
        now = datetime.now()
        if now.weekday() >= 5:  # 跳过周末
            return False
        trading_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
        trading_end = now.replace(hour=14, minute=55, second=0, microsecond=0)
        return trading_start <= now <= trading_end

    def is_stock_selection_time(self):
        """检查是否在选股时间内（策略1实时选股）"""
        now = datetime.now()
        if now.weekday() >= 5:
            return False
        # 策略1在交易时间内实时选股
        trading_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
        trading_end = now.replace(hour=14, minute=55, second=0, microsecond=0)
        return trading_start <= now <= trading_end

    def get_stock_data(self, stock_code, period='1d', count=10):
        """获取股票数据"""
        try:
            data = xtdata.get_market_data(
                stock_list=[stock_code],
                period=period,
                count=count,
                fill_data=True
            )
            return data
        except Exception as e:
            logger.error(f"策略1 - 获取股票数据失败 {stock_code}: {e}")
            return None

    def is_main_board_stock(self, stock_code):
        """判断是否为主板股票"""
        # 主板：60开头（上海主板）、00开头（深圳主板）
        # 排除：30开头（创业板）、68开头（科创板）、8开头（新三板）
        return stock_code.startswith('60') or stock_code.startswith('00')

    def is_new_stock(self, stock_code):
        """判断是否为新股（上市不足60天）"""
        try:
            # 获取股票基本信息
            info = xtdata.get_instrument_detail(stock_code)
            if info is None:
                return True  # 无法获取信息的股票视为新股，排除

            # 检查上市时间
            list_date = info.get('ListDate', '')
            if not list_date:
                return True

            # 计算上市天数
            from datetime import datetime
            list_datetime = datetime.strptime(str(list_date), '%Y%m%d')
            days_since_listing = (datetime.now() - list_datetime).days

            return days_since_listing < 60  # 上市不足60天视为新股
        except:
            return True  # 出错时视为新股，排除

    def is_st_stock(self, stock_code):
        """判断是否为ST股票"""
        try:
            info = xtdata.get_instrument_detail(stock_code)
            name = info.get('name', '')
            return 'ST' in name or '*ST' in name
        except:
            return False

    def is_delisted_stock(self, stock_code):
        """判断是否为退市股票"""
        try:
            info = xtdata.get_instrument_detail(stock_code)
            name = info.get('name', '')
            return '退' in name or name.startswith('*')
        except:
            return False

    def is_limit_up(self, stock_code, current_price, prev_close):
        """判断是否涨停"""
        if self.is_main_board_stock(stock_code):
            limit_up_price = prev_close * 1.1  # 主板10%
        else:
            limit_up_price = prev_close * 1.2  # 创业板20%
        return abs(current_price - limit_up_price) < 0.01

    def calculate_price_change_rate(self, current_price, prev_close):
        """计算涨幅"""
        if prev_close == 0:
            return 0
        return (current_price - prev_close) / prev_close * 100

    def p1p2_stock_selection(self):
        """P1P2选股逻辑（实时执行）
        通达信公式：
        P1:=IF(C<REF(O,1) AND C<O,1,0);
        P2:=IF(C<REF(O,2) AND C<O,1,0);
        选股条件: P1=0 AND REF(P1,1)=1 AND P2=0 AND REF(P2,1)=1;
        """
        try:
            all_stocks = xtdata.get_stock_list_in_sector('A股')
            selected_stocks = []
            processed_count = 0
            filtered_count = 0

            logger.info(f"策略1 - 开始P1P2实时选股，共{len(all_stocks)}只股票")

            # 大幅增加处理数量，确保有足够的股票参与选股
            for stock_code in all_stocks:  # 处理所有股票
                try:
                    processed_count += 1

                    # 基本过滤条件 - 只保留最基本的过滤
                    if not self.is_main_board_stock(stock_code):
                        continue

                    # 暂时完全放开其他过滤条件，专注于找到符合P1P2条件的股票
                    # 后续可以根据需要逐步加回过滤条件

                    filtered_count += 1

                    # 获取历史数据（需要至少4天数据来计算P1、P2）
                    data = self.get_stock_data(stock_code, period='1d', count=10)  # 增加数据量
                    if data is None:
                        logger.debug(f"策略1 - {stock_code} 无法获取数据")
                        continue

                    if len(data) < 4:
                        logger.debug(f"策略1 - {stock_code} 数据不足，只有{len(data)}天")
                        continue

                    # 获取价格数据
                    closes = data['close'].values
                    opens = data['open'].values

                    if len(closes) < 4 or len(opens) < 4:
                        continue

                    # 重新理解通达信公式
                    # 在通达信中，当前K线是最新的完整K线（昨日收盘后的数据）
                    # 所以我们需要用昨日的数据作为"当前"来计算

                    # 使用昨日数据作为当前计算基准
                    current_close = closes[-2]    # 昨日收盘价（作为当前）
                    current_open = opens[-2]      # 昨日开盘价（作为当前）

                    # REF(O,1) 是前一日开盘价
                    ref_open_1 = opens[-3] if len(opens) >= 3 else opens[-2]

                    # REF(O,2) 是前两日开盘价
                    ref_open_2 = opens[-4] if len(opens) >= 4 else opens[-3]

                    # 前一日数据（用于计算REF(P1,1)和REF(P2,1)）
                    prev_close = closes[-3] if len(closes) >= 3 else closes[-2]
                    prev_open = opens[-3] if len(opens) >= 3 else opens[-2]
                    prev_ref_open_1 = opens[-4] if len(opens) >= 4 else opens[-3]
                    prev_ref_open_2 = opens[-5] if len(opens) >= 5 else opens[-4]

                    # 计算P1指标：P1:=IF(C<REF(O,1) AND C<O,1,0);
                    # 当前P1：当前收盘价 < REF(O,1) AND 当前收盘价 < 当前开盘价
                    p1_current = 1 if (current_close < ref_open_1 and current_close < current_open) else 0
                    # 前一日P1
                    p1_prev = 1 if (prev_close < prev_ref_open_1 and prev_close < prev_open) else 0

                    # 计算P2指标：P2:=IF(C<REF(O,2) AND C<O,1,0);
                    # 当前P2：当前收盘价 < REF(O,2) AND 当前收盘价 < 当前开盘价
                    p2_current = 1 if (current_close < ref_open_2 and current_close < current_open) else 0
                    # 前一日P2
                    p2_prev = 1 if (prev_close < prev_ref_open_2 and prev_close < prev_open) else 0

                    # 选股条件: P1=0 AND REF(P1,1)=1 AND P2=0 AND REF(P2,1)=1
                    condition1 = p1_current == 0 and p1_prev == 1
                    condition2 = p2_current == 0 and p2_prev == 1

                    # 增强调试信息：记录更多股票的计算过程
                    if processed_count <= 50 or condition1 or condition2:  # 记录前50只股票或有条件满足的股票
                        logger.info(f"策略1调试 - {stock_code}: P1当前={p1_current}, P1前={p1_prev}, P2当前={p2_current}, P2前={p2_prev}, 条件1={condition1}, 条件2={condition2}")

                    # 如果任一条件满足，也记录下来
                    if condition1 and not condition2:
                        logger.info(f"策略1 - {stock_code} 满足条件1但不满足条件2")
                    elif condition2 and not condition1:
                        logger.info(f"策略1 - {stock_code} 满足条件2但不满足条件1")

                    # 排除当前已经涨停的股票
                    try:
                        current_data = xtdata.get_market_data([stock_code], period='tick', count=1)
                        if current_data is not None:
                            tick_price = current_data['lastPrice'][0]
                            prev_close_tick = current_data['preClose'][0]
                            if self.is_limit_up(stock_code, tick_price, prev_close_tick):
                                logger.debug(f"策略1 - {stock_code} 已涨停，排除")
                                continue
                    except:
                        pass  # 如果获取实时数据失败，不影响选股

                    if condition1 and condition2:
                        selected_stocks.append(stock_code)
                        logger.info(f"策略1 - ✓ 选中股票: {stock_code}, P1当前={p1_current}, P1前={p1_prev}, P2当前={p2_current}, P2前={p2_prev}")

                except Exception as e:
                    logger.error(f"策略1 - 处理股票{stock_code}时出错: {e}")
                    continue

            logger.info(f"策略1 - 选股完成，处理{processed_count}只股票，通过基础过滤{filtered_count}只，最终选中{len(selected_stocks)}只")

            # 如果没有选中任何股票，输出一些统计信息帮助调试
            if len(selected_stocks) == 0:
                logger.warning("策略1 - 未选中任何股票，可能原因：")
                logger.warning("1. P1P2条件过于严格，实际市场中很少有股票同时满足")
                logger.warning("2. 数据获取问题，无法获取足够的历史数据")
                logger.warning("3. 当前市场状态不符合P1P2反转信号")
                logger.warning("建议：检查通达信选股结果，对比分析差异")

            return selected_stocks

        except Exception as e:
            logger.error(f"策略1 - 选股失败: {e}")
            return []

    def check_buy_condition(self, stock_code):
        """检查买入条件：涨幅达到9.96%"""
        try:
            current_data = xtdata.get_market_data([stock_code], period='tick', count=1)
            if current_data is None:
                return False

            current_price = current_data['lastPrice'][0]
            prev_close = current_data['preClose'][0]
            price_change_rate = self.calculate_price_change_rate(current_price, prev_close)

            return price_change_rate >= 9.96

        except Exception as e:
            logger.error(f"策略1 - 检查买入条件失败 {stock_code}: {e}")
            return False

    def place_buy_order(self, stock_code):
        """下买单（动态半仓）"""
        try:
            # 获取账户资金
            account_info = self.trader.query_stock_asset(self.account)
            if account_info is None:
                logger.error("策略1 - 无法获取账户信息")
                return False

            available_cash = account_info.cash

            # 动态计算仓位：第一只股票用一半资金，第二只股票用剩余资金
            if len(self.bought_stocks) == 0:
                # 第一只股票：用一半资金
                position_cash = available_cash * 0.5
            else:
                # 第二只股票：用剩余资金
                position_cash = available_cash * 0.95  # 留5%作为手续费缓冲

            # 获取当前价格
            current_data = xtdata.get_market_data([stock_code], period='tick', count=1)
            if current_data is None:
                logger.error(f"策略1 - 无法获取{stock_code}当前价格")
                return False

            current_price = current_data['lastPrice'][0]
            buy_quantity = int(position_cash / current_price / 100) * 100

            if buy_quantity < 100:
                logger.warning(f"策略1 - 资金不足，无法买入{stock_code}")
                return False

            # 下买单
            order_id = self.trader.order_stock(
                account=self.account,
                stock_code=stock_code,
                order_type=xtconstant.STOCK_BUY,
                order_volume=buy_quantity,
                price_type=xtconstant.FIX_PRICE,
                price=current_price,
                strategy_name='strategy1_p1p2',
                order_remark=f'P1P2_{stock_code}'
            )

            if order_id > 0:
                logger.info(f"策略1 - 成功下买单: {stock_code}, 数量: {buy_quantity}, 价格: {current_price}, 仓位: {'第一只' if len(self.bought_stocks) == 0 else '第二只'}")
                self.bought_stocks.add(stock_code)
                return True
            else:
                logger.error(f"策略1 - 下买单失败: {stock_code}")
                return False

        except Exception as e:
            logger.error(f"策略1 - 下买单异常 {stock_code}: {e}")
            return False

    def start_trading(self):
        """启动交易策略"""
        self.is_running = True
        logger.info("策略1 - P1P2交易策略已启动")

        # 启动主交易线程
        trading_thread = threading.Thread(target=self.main_trading_loop)
        trading_thread.daemon = True
        trading_thread.start()

    def main_trading_loop(self):
        """主交易循环"""
        last_selection_time = None
        selection_interval = 300  # 5分钟重新选股一次

        while self.is_running:
            try:
                current_time = datetime.now()

                # 交易时间内实时选股和监控
                if self.is_trading_time():
                    # 实时选股逻辑：每5分钟重新选股一次
                    if (last_selection_time is None or
                        (current_time - last_selection_time).total_seconds() >= selection_interval):

                        logger.info("策略1 - 开始P1P2实时选股...")
                        new_selected_stocks = self.p1p2_stock_selection()

                        # 合并新选出的股票到现有列表中（避免重复）
                        for stock in new_selected_stocks:
                            if stock not in self.selected_stocks:
                                self.selected_stocks.append(stock)

                        last_selection_time = current_time
                        logger.info(f"策略1 - 选股完成，当前监控{len(self.selected_stocks)}只股票")

                    # 监控买入条件
                    for stock_code in self.selected_stocks[:]:  # 使用切片避免迭代时修改列表
                        if stock_code not in self.bought_stocks:
                            if self.check_buy_condition(stock_code):
                                if self.place_buy_order(stock_code):
                                    # 买入成功后，如果已买入2只股票，停止监控
                                    if len(self.bought_stocks) >= 2:
                                        logger.info("策略1 - 已买入2只股票，停止监控")
                                        break

                time.sleep(5)  # 5秒检查一次

            except Exception as e:
                logger.error(f"策略1 - 主交易循环异常: {e}")
                time.sleep(10)

    def run_strategy(self):
        """运行策略"""
        logger.info("策略1 - 启动P1P2自动交易策略...")
        logger.info("策略1 - 注意：本策略在9:30开盘后进行选股")
        self.start_trading()

        try:
            while self.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("策略1 - 接收到退出信号")
            self.is_running = False
            self.trader.unsubscribe(self.account)
            self.trader.stop()


if __name__ == "__main__":
    # 配置参数
    ACCOUNT_ID = "**********"  # 您的资金账号

    try:
        # 创建并运行策略1
        strategy1 = Strategy1_P1P2_Trading(ACCOUNT_ID)
        strategy1.run_strategy()

    except Exception as e:
        logger.error(f"策略1 - 程序运行异常: {e}")
        print(f"策略1错误: {e}")
        print("请检查QMT客户端是否已启动并登录")